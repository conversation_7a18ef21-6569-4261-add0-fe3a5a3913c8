# Create Test Appointment Data

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Create Test Appointment Data ===" -ForegroundColor Green

# 1. Login as patient to create appointment
Write-Host "`n1. Patient Login" -ForegroundColor Yellow

$patientLoginData = @{
    phoneNumber = "13800000001"
    password = "123456"
} | ConvertTo-Json

try {
    $patientLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $patientLoginData -Headers $headers
    Write-Host "✅ Patient Login Success" -ForegroundColor Green
    
    $patientToken = $patientLoginResponse.data.token
    $patientHeaders = $headers.Clone()
    $patientHeaders["Authorization"] = "Bearer $patientToken"
    
} catch {
    Write-Host "❌ Patient Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Check patient's health profiles
Write-Host "`n2. Check Patient Health Profiles" -ForegroundColor Yellow

try {
    $profilesUrl = "$baseUrl/api/health/profiles?page=1&size=10"
    $profilesResponse = Invoke-RestMethod -Uri $profilesUrl -Method GET -Headers $patientHeaders
    
    Write-Host "✅ Health Profiles API Success" -ForegroundColor Green
    Write-Host "Total Profiles: $($profilesResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($profilesResponse.data.content -and $profilesResponse.data.content.Count -gt 0) {
        $profile = $profilesResponse.data.content[0]
        Write-Host "First Profile:" -ForegroundColor White
        Write-Host "  ID: $($profile.id)" -ForegroundColor Gray
        Write-Host "  Name: $($profile.profileOwnerName)" -ForegroundColor Gray
        Write-Host "  Gender: $($profile.gender)" -ForegroundColor Gray
        Write-Host "  Age: $($profile.age)" -ForegroundColor Gray
        
        $profileId = $profile.id
    } else {
        Write-Host "⚠️ No health profiles found, creating one..." -ForegroundColor Yellow
        
        # Create a health profile
        $createProfileData = @{
            profileOwnerName = "张三"
            gender = "MALE"
            birthDate = "1990-01-01"
            idCardNumber = "110101199001011234"
            medicalHistory = "无特殊病史"
        } | ConvertTo-Json
        
        try {
            $createProfileResponse = Invoke-RestMethod -Uri "$baseUrl/api/health/profiles" -Method POST -Body $createProfileData -Headers $patientHeaders
            Write-Host "✅ Health Profile Created" -ForegroundColor Green
            $profileId = $createProfileResponse.data.id
        } catch {
            Write-Host "❌ Failed to create health profile: $($_.Exception.Message)" -ForegroundColor Red
            exit 1
        }
    }
    
} catch {
    Write-Host "❌ Health Profiles API Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Check available doctor schedules
Write-Host "`n3. Check Available Doctor Schedules" -ForegroundColor Yellow

try {
    $schedulesUrl = "$baseUrl/api/appointments/schedules?page=1&size=10"
    $schedulesResponse = Invoke-RestMethod -Uri $schedulesUrl -Method GET -Headers $patientHeaders
    
    Write-Host "✅ Doctor Schedules API Success" -ForegroundColor Green
    Write-Host "Total Schedules: $($schedulesResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($schedulesResponse.data.content -and $schedulesResponse.data.content.Count -gt 0) {
        $schedule = $schedulesResponse.data.content[0]
        Write-Host "First Schedule:" -ForegroundColor White
        Write-Host "  ID: $($schedule.id)" -ForegroundColor Gray
        Write-Host "  Doctor: $($schedule.doctorName)" -ForegroundColor Gray
        Write-Host "  Date: $($schedule.scheduleDate)" -ForegroundColor Gray
        Write-Host "  Time: $($schedule.startTime) - $($schedule.endTime)" -ForegroundColor Gray
        Write-Host "  Available Slots: $($schedule.availableSlots)" -ForegroundColor Gray
        
        $scheduleId = $schedule.id
    } else {
        Write-Host "⚠️ No doctor schedules found" -ForegroundColor Yellow
        Write-Host "You need to create doctor schedules first" -ForegroundColor White
        exit 1
    }
    
} catch {
    Write-Host "❌ Doctor Schedules API Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. Create appointment
Write-Host "`n4. Create Test Appointment" -ForegroundColor Yellow

$createAppointmentData = @{
    scheduleId = $scheduleId
    profileId = $profileId
    notes = "测试预约 - 用于验证患者信息显示"
} | ConvertTo-Json

try {
    $createAppointmentResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments" -Method POST -Body $createAppointmentData -Headers $patientHeaders
    Write-Host "✅ Appointment Created Successfully" -ForegroundColor Green
    Write-Host "Appointment ID: $($createAppointmentResponse.data.id)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Failed to create appointment: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 5. Login as doctor and check appointments
Write-Host "`n5. Test Doctor View of New Appointment" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "18610001005"
    password = "123456"
} | ConvertTo-Json

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "✅ Doctor Login Success" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $doctorHeaders = $headers.Clone()
    $doctorHeaders["Authorization"] = "Bearer $doctorToken"
    
    # Check doctor appointments
    $doctorAppointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
    $doctorAppointmentsResponse = Invoke-RestMethod -Uri $doctorAppointmentsUrl -Method GET -Headers $doctorHeaders
    
    Write-Host "✅ Doctor Appointments API Success" -ForegroundColor Green
    Write-Host "Total Appointments: $($doctorAppointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($doctorAppointmentsResponse.data.content -and $doctorAppointmentsResponse.data.content.Count -gt 0) {
        Write-Host "`n📋 Patient Info in Doctor View:" -ForegroundColor White
        
        $appointment = $doctorAppointmentsResponse.data.content[0]
        Write-Host "  Appointment ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  Patient Name: '$($appointment.profileOwnerName)'" -ForegroundColor Yellow
        Write-Host "  Patient Gender: '$($appointment.profileGender)'" -ForegroundColor Yellow
        Write-Host "  Patient Age: '$($appointment.profileAge)'" -ForegroundColor Yellow
        Write-Host "  Patient Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Yellow
        Write-Host "  Patient Birth Date: '$($appointment.profileBirthDate)'" -ForegroundColor Yellow
        Write-Host "  Appointment Date: '$($appointment.appointmentDate)'" -ForegroundColor Green
        Write-Host "  Appointment Time: '$($appointment.appointmentTime)'" -ForegroundColor Green
        Write-Host "  Doctor Name: '$($appointment.doctorName)'" -ForegroundColor Cyan
        Write-Host "  Department: '$($appointment.departmentName)'" -ForegroundColor Cyan
        
        # Verify the fix worked
        if ($appointment.profileOwnerName -and $appointment.profileOwnerName -ne "null" -and $appointment.profileOwnerName -ne "") {
            Write-Host "`n🎉 SUCCESS: Patient name is now displaying correctly!" -ForegroundColor Green
        } else {
            Write-Host "`n❌ ISSUE: Patient name is still missing" -ForegroundColor Red
        }
        
        if ($appointment.profileGender -and $appointment.profileGender -ne "null") {
            Write-Host "🎉 SUCCESS: Patient gender is now displaying correctly!" -ForegroundColor Green
        } else {
            Write-Host "❌ ISSUE: Patient gender is still missing" -ForegroundColor Red
        }
        
        if ($appointment.profileAge) {
            Write-Host "🎉 SUCCESS: Patient age is now displaying correctly!" -ForegroundColor Green
        } else {
            Write-Host "❌ ISSUE: Patient age is still missing" -ForegroundColor Red
        }
        
    } else {
        Write-Host "⚠️ No appointments found in doctor view" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Doctor test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Appointment Creation Complete ===" -ForegroundColor Green
