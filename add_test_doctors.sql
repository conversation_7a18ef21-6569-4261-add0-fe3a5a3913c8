-- 为各科室添加测试医生数据

-- 首先查看当前医生数据
SELECT 'Current Doctors:' as info;
SELECT d.user_id, d.real_name, d.department_id, dept.name as department_name, d.title, d.status 
FROM doctors d 
LEFT JOIN departments dept ON d.department_id = dept.id 
ORDER BY d.department_id;

-- 查看当前用户数据，找到可用的用户ID
SELECT 'Available Users for Doctor Assignment:' as info;
SELECT u.id, u.phone_number, u.role, u.nickname 
FROM users u 
WHERE u.role = 'DOCTOR' AND u.id NOT IN (SELECT user_id FROM doctors WHERE user_id IS NOT NULL)
ORDER BY u.id;

-- 为没有医生的科室添加医生
-- 假设我们有一些医生用户但还没有分配到科室

-- 首先为测试医生用户(user_id=14)分配到内科，如果还没有的话
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
-- 确保测试医生在内科
(14, 'Test Doctor', 1, '主治医师', '内科常见疾病诊治', '经验丰富的内科医生', 'APPROVED', 'MALE', '1980-01-01');

-- 为其他科室添加测试医生（使用现有的医生用户ID）
-- 注意：这里需要根据实际的用户ID来调整

-- 为中医科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(15, '张中医', 5, '主任医师', '中医内科、针灸推拿', '30年中医临床经验', 'APPROVED', 'MALE', '1970-05-15'),
(16, '李中医', 5, '副主任医师', '中医妇科、中药调理', '中医妇科专家', 'APPROVED', 'FEMALE', '1975-08-20');

-- 为妇产科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(17, '王妇产', 6, '主任医师', '产科、妇科手术', '妇产科专家，擅长各类妇科手术', 'APPROVED', 'FEMALE', '1978-03-10'),
(18, '赵产科', 6, '主治医师', '产前检查、分娩', '产科专业，温柔细心', 'APPROVED', 'FEMALE', '1982-11-25');

-- 为心理科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(19, '心理师', 9, '主治医师', '心理咨询、心理治疗', '专业心理咨询师', 'APPROVED', 'FEMALE', '1985-07-12');

-- 为皮肤科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(20, '皮肤专家', 4, '副主任医师', '皮肤病、美容皮肤科', '皮肤科专家', 'APPROVED', 'MALE', '1979-09-18');

-- 为眼科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(21, '眼科医生', 7, '主治医师', '眼科常见病、近视治疗', '眼科专业医师', 'APPROVED', 'MALE', '1983-12-05');

-- 为耳鼻喉科添加医生
INSERT IGNORE INTO doctors (
    user_id, 
    real_name, 
    department_id, 
    title, 
    specialty, 
    bio, 
    status,
    gender,
    birth_date
) VALUES 
(22, '耳鼻喉专家', 8, '主任医师', '耳鼻喉科疾病、手术', '耳鼻喉科资深专家', 'APPROVED', 'MALE', '1972-04-22');

-- 检查插入结果
SELECT 'Updated Doctors by Department:' as info;
SELECT 
    dept.name as department_name,
    COUNT(d.user_id) as doctor_count,
    GROUP_CONCAT(d.real_name SEPARATOR ', ') as doctors
FROM departments dept
LEFT JOIN doctors d ON dept.id = d.department_id AND d.status = 'APPROVED'
GROUP BY dept.id, dept.name
ORDER BY dept.id;
