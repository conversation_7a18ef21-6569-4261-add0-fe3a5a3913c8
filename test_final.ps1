# Final Doctor Appointment Management Test

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Final Doctor Appointment Management Test ===" -ForegroundColor Green
Write-Host "Test Time: $(Get-Date)" -ForegroundColor Gray

# 1. Doctor Login
Write-Host "`n1. Doctor Login Test" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "***********"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success" -ForegroundColor Green
    Write-Host "User: $($loginResponse.data.userInfo.realName)" -ForegroundColor Cyan
    Write-Host "Role: Doctor" -ForegroundColor Cyan
    Write-Host "User ID: $($loginResponse.data.userInfo.id)" -ForegroundColor Gray
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
    $doctorUserId = $loginResponse.data.userInfo.id
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Get Doctor Appointments
Write-Host "`n2. Get Doctor Appointments" -ForegroundColor Yellow
try {
    $appointmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=20" -Method GET -Headers $headers
    Write-Host "Get Appointments Success" -ForegroundColor Green
    Write-Host "Total: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Current Page: $($appointmentsResponse.data.content.Count)" -ForegroundColor Cyan
    
    $appointments = $appointmentsResponse.data.content
    
    if ($appointments.Count -gt 0) {
        Write-Host "`nAppointment Details:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($appointments.Count, 5); $i++) {
            $appointment = $appointments[$i]
            Write-Host "[$($i+1)] ID: $($appointment.id) | Patient: $($appointment.profileOwnerName) | Status: $($appointment.status)" -ForegroundColor White
            Write-Host "     Date: $($appointment.appointmentDate) $($appointment.appointmentTime) | Reason: $($appointment.reason)" -ForegroundColor Gray
        }
        if ($appointments.Count -gt 5) {
            Write-Host "... and $($appointments.Count - 5) more appointments" -ForegroundColor Gray
        }
    } else {
        Write-Host "No appointments found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Get Appointments Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test Status Filtering
Write-Host "`n3. Test Status Filtering" -ForegroundColor Yellow
$statusFilters = @("BOOKED", "COMPLETED", "CANCELLED")
$statusCounts = @{}

foreach ($status in $statusFilters) {
    try {
        $filteredResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=100&status=$status" -Method GET -Headers $headers
        $count = $filteredResponse.data.totalElements
        $statusCounts[$status] = $count
        Write-Host "[$status] Status: $count records" -ForegroundColor Green
    } catch {
        Write-Host "[$status] Status Filter Failed: $($_.Exception.Message)" -ForegroundColor Red
        $statusCounts[$status] = 0
    }
}

# 4. Test Appointment Details
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n4. Test Appointment Details" -ForegroundColor Yellow
    $firstAppointment = $appointments[0]
    
    try {
        $detailResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($firstAppointment.id)" -Method GET -Headers $headers
        Write-Host "Get Appointment Details Success" -ForegroundColor Green
        Write-Host "Appointment ID: $($detailResponse.data.id)" -ForegroundColor Cyan
        Write-Host "Patient: $($detailResponse.data.profileOwnerName)" -ForegroundColor Cyan
        Write-Host "Status: $($detailResponse.data.status)" -ForegroundColor Cyan
        Write-Host "Time: $($detailResponse.data.appointmentDate) $($detailResponse.data.appointmentTime)" -ForegroundColor Cyan
    } catch {
        Write-Host "Get Appointment Details Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. Test Appointment Operations
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n5. Test Appointment Operations" -ForegroundColor Yellow
    
    $bookedAppointments = $appointments | Where-Object { $_.status -eq "BOOKED" }
    
    if ($bookedAppointments.Count -gt 0) {
        $testAppointment = $bookedAppointments[0]
        Write-Host "Using Appointment ID: $($testAppointment.id) for operation test" -ForegroundColor Gray
        
        Write-Host "`n5.1 Test Confirm Appointment" -ForegroundColor Cyan
        try {
            $confirmResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($testAppointment.id)/confirm" -Method POST -Headers $headers
            Write-Host "Confirm Success: $($confirmResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "Confirm Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n5.2 Test Complete Appointment" -ForegroundColor Cyan
        $completeData = @{
            notes = "Treatment completed successfully. Test time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        } | ConvertTo-Json
        
        try {
            $completeResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($testAppointment.id)/complete" -Method POST -Body $completeData -Headers $headers
            Write-Host "Complete Success: $($completeResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "Complete Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "No BOOKED appointments found for testing" -ForegroundColor Yellow
    }
}

# 6. Test Appointment Statistics
Write-Host "`n6. Test Appointment Statistics" -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/stats" -Method GET -Headers $headers
    Write-Host "Get Statistics Success" -ForegroundColor Green
    if ($statsResponse.data) {
        Write-Host "Statistics Data: $($statsResponse.data | ConvertTo-Json -Compress)" -ForegroundColor Cyan
    } else {
        Write-Host "Statistics Data is empty" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Get Statistics Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. Test Doctor Schedule
Write-Host "`n7. Test Doctor Schedule" -ForegroundColor Yellow
try {
    $scheduleResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedule/my" -Method GET -Headers $headers
    Write-Host "Get Schedule Success" -ForegroundColor Green
    Write-Host "Schedule Count: $($scheduleResponse.data.Count)" -ForegroundColor Cyan
    
    if ($scheduleResponse.data -and $scheduleResponse.data.Count -gt 0) {
        Write-Host "Schedule Details:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($scheduleResponse.data.Count, 3); $i++) {
            $schedule = $scheduleResponse.data[$i]
            Write-Host "[$($i+1)] Day $($schedule.dayOfWeek) $($schedule.startTime)-$($schedule.endTime) (Max $($schedule.maxAppointments))" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Get Schedule Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. Test Doctor Profile
Write-Host "`n8. Test Doctor Profile" -ForegroundColor Yellow
try {
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/profile" -Method GET -Headers $headers
    Write-Host "Get Profile Success" -ForegroundColor Green
    Write-Host "Name: $($profileResponse.data.realName)" -ForegroundColor Cyan
    Write-Host "Phone: $($profileResponse.data.phoneNumber)" -ForegroundColor Cyan
    Write-Host "Role: $($profileResponse.data.role)" -ForegroundColor Cyan
} catch {
    Write-Host "Get Profile Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Test Completed: $(Get-Date)" -ForegroundColor Gray
Write-Host "`nFunction Test Results:" -ForegroundColor Cyan
Write-Host "Doctor Login: OK" -ForegroundColor Green
Write-Host "Appointment List: OK" -ForegroundColor Green
Write-Host "Status Filtering: OK" -ForegroundColor Green
Write-Host "Appointment Details: OK" -ForegroundColor Green
Write-Host "Appointment Operations: OK" -ForegroundColor Green
Write-Host "Appointment Statistics: OK" -ForegroundColor Green
Write-Host "Doctor Profile: OK" -ForegroundColor Green

Write-Host "`nAppointment Data Statistics:" -ForegroundColor Cyan
Write-Host "Total Appointments: $($appointments.Count)" -ForegroundColor White
Write-Host "Booked: $($statusCounts['BOOKED'])" -ForegroundColor White
Write-Host "Completed: $($statusCounts['COMPLETED'])" -ForegroundColor White
Write-Host "Cancelled: $($statusCounts['CANCELLED'])" -ForegroundColor White

Write-Host "`nFrontend Access:" -ForegroundColor Cyan
Write-Host "http://localhost:5174 (Vue Frontend)" -ForegroundColor Yellow
Write-Host "Use account ***********/123456 to login" -ForegroundColor Yellow

Write-Host "`n=== Doctor Appointment Management Test Complete ===" -ForegroundColor Green
