# Test Frontend API through Proxy

$frontendUrl = "http://localhost:5174"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Frontend API through Proxy ===" -ForegroundColor Green

# 1. Test Login through Frontend Proxy
Write-Host "`n1. Test Login through Frontend Proxy" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$frontendUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success through Proxy" -ForegroundColor Green
    Write-Host "Response Code: $($loginResponse.code)" -ForegroundColor Cyan
    Write-Host "User: $($loginResponse.data.userInfo.realName)" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Login Failed through Proxy: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test Departments through Frontend Proxy
Write-Host "`n2. Test Departments through Frontend Proxy" -ForegroundColor Yellow
try {
    $departmentsResponse = Invoke-RestMethod -Uri "$frontendUrl/api/appointments/departments" -Method GET -Headers $headers
    Write-Host "Get Departments Success through Proxy" -ForegroundColor Green
    Write-Host "Response Code: $($departmentsResponse.code)" -ForegroundColor Cyan
    Write-Host "Department Count: $($departmentsResponse.data.Count)" -ForegroundColor Cyan
    
    $departments = $departmentsResponse.data
    
    if ($departments.Count -gt 0) {
        Write-Host "`nDepartment List:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $departments.Count; $i++) {
            $dept = $departments[$i]
            Write-Host "[$($i+1)] ID: $($dept.id) | Name: $($dept.name)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Get Departments Failed through Proxy: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 3. Test Department Doctors through Frontend Proxy
Write-Host "`n3. Test Department Doctors through Frontend Proxy" -ForegroundColor Yellow

# Test Internal Medicine (ID: 1)
try {
    $doctorsResponse = Invoke-RestMethod -Uri "$frontendUrl/api/appointments/departments/1/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "Get Internal Medicine Doctors Success through Proxy" -ForegroundColor Green
    Write-Host "Response Code: $($doctorsResponse.code)" -ForegroundColor Cyan
    Write-Host "Response Structure:" -ForegroundColor Cyan
    
    # Check response structure
    if ($doctorsResponse.data) {
        Write-Host "Data Type: $($doctorsResponse.data.GetType().Name)" -ForegroundColor White
        
        if ($doctorsResponse.data.content) {
            Write-Host "Content Count: $($doctorsResponse.data.content.Count)" -ForegroundColor White
            Write-Host "Total Elements: $($doctorsResponse.data.totalElements)" -ForegroundColor White
            
            if ($doctorsResponse.data.content.Count -gt 0) {
                Write-Host "`nDoctors in Internal Medicine:" -ForegroundColor White
                foreach ($doctor in $doctorsResponse.data.content) {
                    Write-Host "  - $($doctor.realName) | Title: $($doctor.title) | Status: $($doctor.status)" -ForegroundColor Gray
                }
            } else {
                Write-Host "No doctors found in Internal Medicine" -ForegroundColor Yellow
            }
        } else {
            Write-Host "No content property in response data" -ForegroundColor Yellow
            Write-Host "Response Data: $($doctorsResponse.data | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        }
    } else {
        Write-Host "No data property in response" -ForegroundColor Yellow
        Write-Host "Full Response: $($doctorsResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
    }
} catch {
    Write-Host "Get Internal Medicine Doctors Failed through Proxy: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 4. Test Surgery Department (ID: 2)
Write-Host "`n4. Test Surgery Department through Frontend Proxy" -ForegroundColor Yellow
try {
    $surgeryDoctorsResponse = Invoke-RestMethod -Uri "$frontendUrl/api/appointments/departments/2/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "Get Surgery Doctors Success through Proxy" -ForegroundColor Green
    Write-Host "Response Code: $($surgeryDoctorsResponse.code)" -ForegroundColor Cyan
    Write-Host "Total Elements: $($surgeryDoctorsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Content Count: $($surgeryDoctorsResponse.data.content.Count)" -ForegroundColor Cyan
    
    if ($surgeryDoctorsResponse.data.content.Count -gt 0) {
        Write-Host "Surgery Doctors:" -ForegroundColor White
        foreach ($doctor in $surgeryDoctorsResponse.data.content) {
            Write-Host "  - $($doctor.realName) | Title: $($doctor.title)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Get Surgery Doctors Failed through Proxy: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Test Pediatrics Department (ID: 3)
Write-Host "`n5. Test Pediatrics Department through Frontend Proxy" -ForegroundColor Yellow
try {
    $pediatricsDoctorsResponse = Invoke-RestMethod -Uri "$frontendUrl/api/appointments/departments/3/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "Get Pediatrics Doctors Success through Proxy" -ForegroundColor Green
    Write-Host "Response Code: $($pediatricsDoctorsResponse.code)" -ForegroundColor Cyan
    Write-Host "Total Elements: $($pediatricsDoctorsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Content Count: $($pediatricsDoctorsResponse.data.content.Count)" -ForegroundColor Cyan
    
    if ($pediatricsDoctorsResponse.data.content.Count -gt 0) {
        Write-Host "Pediatrics Doctors:" -ForegroundColor White
        foreach ($doctor in $pediatricsDoctorsResponse.data.content) {
            Write-Host "  - $($doctor.realName) | Title: $($doctor.title)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Get Pediatrics Doctors Failed through Proxy: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Frontend API Test Complete ===" -ForegroundColor Green
