# Simple Appointment Test

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Simple Appointment Test ===" -ForegroundColor Green

# Test doctor login and appointments
Write-Host "`n1. Doctor Login Test" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "18610001005"
    password = "123456"
} | ConvertTo-<PERSON>son

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $doctorToken"
    
    # Test appointments API
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my"
    $params = "?page=1&size=10"
    $fullUrl = $appointmentsUrl + $params
    
    $appointmentsResponse = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $authHeaders
    Write-Host "Doctor Appointments API Success" -ForegroundColor Green
    Write-Host "Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        Write-Host "`nFirst Appointment Details:" -ForegroundColor White
        $appointment = $appointmentsResponse.data.content[0]
        
        Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
        
        # Patient Information
        Write-Host "  Patient Name: '$($appointment.profileOwnerName)'" -ForegroundColor Yellow
        Write-Host "  Patient Gender: '$($appointment.profileGender)'" -ForegroundColor Yellow
        Write-Host "  Patient Age: '$($appointment.profileAge)'" -ForegroundColor Yellow
        Write-Host "  Patient Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Yellow
        Write-Host "  Patient Birth Date: '$($appointment.profileBirthDate)'" -ForegroundColor Yellow
        
        # Doctor Information
        Write-Host "  Doctor Name: '$($appointment.doctorName)'" -ForegroundColor Cyan
        Write-Host "  Doctor Title: '$($appointment.doctorTitle)'" -ForegroundColor Cyan
        Write-Host "  Department: '$($appointment.departmentName)'" -ForegroundColor Cyan
        
        # Schedule Information
        Write-Host "  Appointment Date: '$($appointment.appointmentDate)'" -ForegroundColor Green
        Write-Host "  Appointment Time: '$($appointment.appointmentTime)'" -ForegroundColor Green
        Write-Host "  End Time: '$($appointment.endTime)'" -ForegroundColor Green
        
        # Check if the fix worked
        Write-Host "`nFix Verification:" -ForegroundColor White
        if ($appointment.profileOwnerName -and $appointment.profileOwnerName -ne "null") {
            Write-Host "  ✅ Patient name is displaying correctly" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Patient name is still missing" -ForegroundColor Red
        }
        
        if ($appointment.profileGender -and $appointment.profileGender -ne "null") {
            Write-Host "  ✅ Patient gender is displaying correctly" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Patient gender is still missing" -ForegroundColor Red
        }
        
        if ($appointment.profileAge) {
            Write-Host "  ✅ Patient age is displaying correctly" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Patient age is still missing" -ForegroundColor Red
        }
        
        if ($appointment.appointmentDate) {
            Write-Host "  ✅ Appointment date is displaying correctly" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Appointment date is still missing" -ForegroundColor Red
        }
        
    } else {
        Write-Host "No appointments found for this doctor" -ForegroundColor Yellow
        Write-Host "The database may not have appointment data yet" -ForegroundColor White
    }
    
} catch {
    Write-Host "Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green

Write-Host "`nSummary of Applied Fixes:" -ForegroundColor Cyan
Write-Host "1. Extended AppointmentDTO with patient health profile fields" -ForegroundColor White
Write-Host "2. Updated DoctorService.convertToAppointmentDTO method" -ForegroundColor White
Write-Host "3. Updated AppointmentService.convertToAppointmentDTO method" -ForegroundColor White
Write-Host "4. Fixed AppointmentRepository queries with JOIN FETCH" -ForegroundColor White
Write-Host "5. Added proper gender mapping and age calculation" -ForegroundColor White
Write-Host "6. Added patient phone number from User entity" -ForegroundColor White

Write-Host "`nExpected Results:" -ForegroundColor Yellow
Write-Host "- Patient names should display instead of '用户1'" -ForegroundColor White
Write-Host "- Patient ages should display instead of '未知岁'" -ForegroundColor White
Write-Host "- Patient genders should display instead of '未知性'" -ForegroundColor White
Write-Host "- Complete appointment information should be available" -ForegroundColor White
