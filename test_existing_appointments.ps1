# Test Diagnosis with Existing Appointments

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Diagnosis with Existing Appointments ===" -ForegroundColor Green

# Doctor login
Write-Host "`n1. Doctor Login" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "***********"
    password = "123456"
} | ConvertTo-Json

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $doctorAuthHeaders = $headers.Clone()
    $doctorAuthHeaders["Authorization"] = "Bearer $doctorToken"
    
    # Get all appointments (not just doctor's own)
    Write-Host "`n2. Get All Appointments" -ForegroundColor Yellow
    
    try {
        # Try to get all appointments first
        $allAppointmentsUrl = "$baseUrl/api/appointments?page=1&size=20"
        
        try {
            $allAppointmentsResponse = Invoke-RestMethod -Uri $allAppointmentsUrl -Method GET -Headers $doctorAuthHeaders
            Write-Host "All Appointments Retrieved: $($allAppointmentsResponse.data.totalElements)" -ForegroundColor Cyan
            
            if ($allAppointmentsResponse.data.content -and $allAppointmentsResponse.data.content.Count -gt 0) {
                # Find a BOOKED appointment
                $bookedAppointment = $allAppointmentsResponse.data.content | Where-Object { $_.status -eq "BOOKED" } | Select-Object -First 1
                
                if ($bookedAppointment) {
                    Write-Host "`nFound BOOKED Appointment:" -ForegroundColor White
                    Write-Host "  ID: $($bookedAppointment.id)" -ForegroundColor Gray
                    Write-Host "  Patient: $($bookedAppointment.profileOwnerName)" -ForegroundColor Yellow
                    Write-Host "  Status: $($bookedAppointment.status)" -ForegroundColor Gray
                    Write-Host "  Date: $($bookedAppointment.appointmentDate)" -ForegroundColor Cyan
                    Write-Host "  Time: $($bookedAppointment.appointmentTime)" -ForegroundColor Cyan
                    
                    # Test diagnosis creation
                    Write-Host "`n3. Create Diagnosis Prescription" -ForegroundColor Yellow
                    
                    $prescriptionData = @{
                        profileId = $bookedAppointment.profileId
                        appointmentId = $bookedAppointment.id
                        diagnosis = "Routine checkup completed - patient in good health. Test diagnosis from API."
                        medications = @(
                            @{
                                name = "Multivitamin"
                                specification = "1 tablet"
                                quantity = 30
                                frequency = "Once daily"
                                dosage = "1 tablet with breakfast"
                                notes = "General health maintenance"
                            },
                            @{
                                name = "Vitamin D3"
                                specification = "1000 IU"
                                quantity = 60
                                frequency = "Once daily"
                                dosage = "1 capsule with meal"
                                notes = "Bone health support"
                            }
                        )
                    } | ConvertTo-Json -Depth 3
                    
                    try {
                        $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/prescribe"
                        $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $doctorAuthHeaders
                        
                        Write-Host "✅ Prescription Created Successfully!" -ForegroundColor Green
                        Write-Host "Prescription ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
                        Write-Host "Diagnosis: $($prescriptionResponse.data.prescription.diagnosis)" -ForegroundColor Yellow
                        Write-Host "Medications Count: $($prescriptionResponse.data.prescription.medications.Count)" -ForegroundColor Yellow
                        
                        foreach ($med in $prescriptionResponse.data.prescription.medications) {
                            Write-Host "  - $($med.name) ($($med.specification)) x$($med.quantity)" -ForegroundColor Cyan
                        }
                        
                        # Verify appointment status update
                        Write-Host "`n4. Verify Appointment Status Update" -ForegroundColor Yellow
                        
                        Start-Sleep -Seconds 2
                        $updatedResponse = Invoke-RestMethod -Uri $allAppointmentsUrl -Method GET -Headers $doctorAuthHeaders
                        $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $bookedAppointment.id }
                        
                        if ($updatedAppointment) {
                            Write-Host "Updated Appointment Status:" -ForegroundColor Green
                            Write-Host "  Status: $($updatedAppointment.status)" -ForegroundColor Gray
                            Write-Host "  Has Diagnosis: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                            
                            if ($updatedAppointment.status -eq "COMPLETED" -or $updatedAppointment.hasDiagnosis -eq $true) {
                                Write-Host "`n🎉 SUCCESS: Diagnosis Function Working Perfectly!" -ForegroundColor Green
                                Write-Host "   ✅ Prescription created successfully" -ForegroundColor White
                                Write-Host "   ✅ Appointment status updated" -ForegroundColor White
                                Write-Host "   ✅ Diagnosis flag set correctly" -ForegroundColor White
                            } else {
                                Write-Host "`n⚠️  Partial Success: Prescription created but status not updated" -ForegroundColor Yellow
                            }
                        }
                        
                        # Test prescription retrieval
                        Write-Host "`n5. Test Prescription Retrieval" -ForegroundColor Yellow
                        
                        try {
                            $prescriptionDetailUrl = "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/prescription"
                            $prescriptionDetailResponse = Invoke-RestMethod -Uri $prescriptionDetailUrl -Method GET -Headers $doctorAuthHeaders
                            
                            Write-Host "✅ Prescription Retrieved Successfully:" -ForegroundColor Green
                            Write-Host "  Diagnosis: $($prescriptionDetailResponse.data.diagnosis)" -ForegroundColor Yellow
                            Write-Host "  Created: $($prescriptionDetailResponse.data.createdAt)" -ForegroundColor Gray
                            Write-Host "  Medications:" -ForegroundColor Yellow
                            
                            foreach ($med in $prescriptionDetailResponse.data.medications) {
                                Write-Host "    - $($med.name): $($med.dosage)" -ForegroundColor Cyan
                            }
                            
                        } catch {
                            Write-Host "❌ Failed to retrieve prescription: $($_.Exception.Message)" -ForegroundColor Red
                        }
                        
                    } catch {
                        Write-Host "❌ Prescription Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
                        if ($_.ErrorDetails.Message) {
                            Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
                        }
                    }
                    
                } else {
                    Write-Host "No BOOKED appointments found to test with" -ForegroundColor Yellow
                    
                    # Show all appointments for reference
                    Write-Host "`nAll Appointments:" -ForegroundColor White
                    foreach ($apt in $allAppointmentsResponse.data.content) {
                        Write-Host "  ID: $($apt.id), Status: $($apt.status), Patient: $($apt.profileOwnerName)" -ForegroundColor Gray
                    }
                }
                
            } else {
                Write-Host "No appointments found in the system" -ForegroundColor Yellow
            }
            
        } catch {
            Write-Host "Failed to get all appointments: $($_.Exception.Message)" -ForegroundColor Red
            
            # Fallback: try doctor's own appointments
            Write-Host "`nTrying doctor's own appointments..." -ForegroundColor Yellow
            
            $doctorAppointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
            $doctorAppointmentsResponse = Invoke-RestMethod -Uri $doctorAppointmentsUrl -Method GET -Headers $doctorAuthHeaders
            
            Write-Host "Doctor's Appointments: $($doctorAppointmentsResponse.data.totalElements)" -ForegroundColor Cyan
            
            if ($doctorAppointmentsResponse.data.content -and $doctorAppointmentsResponse.data.content.Count -gt 0) {
                foreach ($apt in $doctorAppointmentsResponse.data.content) {
                    Write-Host "  ID: $($apt.id), Status: $($apt.status), Patient: $($apt.profileOwnerName)" -ForegroundColor Gray
                }
            }
        }
        
    } catch {
        Write-Host "Failed to get appointments: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Doctor login failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green

Write-Host "`nFrontend Access:" -ForegroundColor Cyan
Write-Host "- Frontend: http://localhost:5174" -ForegroundColor White
Write-Host "- Doctor Login: http://localhost:5174/login" -ForegroundColor White
Write-Host "- Doctor Appointments: http://localhost:5174/doctor/appointments" -ForegroundColor White
Write-Host "- Test Account: *********** / 123456" -ForegroundColor White

Write-Host "`nDiagnosis Features in Frontend:" -ForegroundColor Yellow
Write-Host "1. Login as doctor (***********/123456)" -ForegroundColor White
Write-Host "2. Go to appointment management page" -ForegroundColor White
Write-Host "3. Click diagnosis button (🩺) for any appointment" -ForegroundColor White
Write-Host "4. Fill diagnosis and prescription form" -ForegroundColor White
Write-Host "5. Submit to create prescription" -ForegroundColor White
Write-Host "6. See updated diagnosis status in appointment list" -ForegroundColor White
