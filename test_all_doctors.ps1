# Test All Doctor Accounts

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test All Doctor Accounts ===" -ForegroundColor Green

# Test all possible doctor accounts
$doctorAccounts = @(
    "***********", "***********", "***********", 
    "***********", "***********", "***********"
)

foreach ($phone in $doctorAccounts) {
    Write-Host "`nTesting Doctor: $phone" -ForegroundColor Yellow
    
    $loginData = @{
        phoneNumber = $phone
        password = "123456"
    } | ConvertTo-Json

    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        
        if ($loginResponse.code -eq 200) {
            Write-Host "  ✅ Login Success" -ForegroundColor Green
            
            $token = $loginResponse.data.token
            $authHeaders = $headers.Clone()
            $authHeaders["Authorization"] = "Bearer $token"
            
            # Test appointments API
            try {
                $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
                $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
                
                Write-Host "  ✅ Appointments API Success" -ForegroundColor Green
                Write-Host "  Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
                
                if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                    Write-Host "  🎯 Found appointments for this doctor!" -ForegroundColor Magenta
                    
                    $appointment = $appointmentsResponse.data.content[0]
                    Write-Host "  📋 First Appointment Details:" -ForegroundColor White
                    Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
                    Write-Host "    Status: $($appointment.status)" -ForegroundColor Gray
                    
                    # Patient Information
                    Write-Host "    Patient Info:" -ForegroundColor Yellow
                    Write-Host "      Name: '$($appointment.profileOwnerName)'" -ForegroundColor Gray
                    Write-Host "      Gender: '$($appointment.profileGender)'" -ForegroundColor Gray
                    Write-Host "      Age: '$($appointment.profileAge)'" -ForegroundColor Gray
                    Write-Host "      Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Gray
                    Write-Host "      Birth Date: '$($appointment.profileBirthDate)'" -ForegroundColor Gray
                    Write-Host "      ID Card: '$($appointment.profileIdCardNumber)'" -ForegroundColor Gray
                    
                    # Doctor Information
                    Write-Host "    Doctor Info:" -ForegroundColor Cyan
                    Write-Host "      Name: '$($appointment.doctorName)'" -ForegroundColor Gray
                    Write-Host "      Title: '$($appointment.doctorTitle)'" -ForegroundColor Gray
                    Write-Host "      Department: '$($appointment.departmentName)'" -ForegroundColor Gray
                    
                    # Schedule Information
                    Write-Host "    Schedule Info:" -ForegroundColor Green
                    Write-Host "      Date: '$($appointment.appointmentDate)'" -ForegroundColor Gray
                    Write-Host "      Time: '$($appointment.appointmentTime)' - '$($appointment.endTime)'" -ForegroundColor Gray
                    
                    # Verify the fix
                    Write-Host "  🔍 Fix Verification:" -ForegroundColor White
                    
                    $issues = @()
                    $fixes = @()
                    
                    if ($appointment.profileOwnerName -and $appointment.profileOwnerName -ne "null" -and $appointment.profileOwnerName -ne "") {
                        $fixes += "Patient name: $($appointment.profileOwnerName)"
                    } else {
                        $issues += "Patient name missing"
                    }
                    
                    if ($appointment.profileGender -and $appointment.profileGender -ne "null") {
                        $fixes += "Patient gender: $($appointment.profileGender)"
                    } else {
                        $issues += "Patient gender missing"
                    }
                    
                    if ($appointment.profileAge) {
                        $fixes += "Patient age: $($appointment.profileAge)"
                    } else {
                        $issues += "Patient age missing"
                    }
                    
                    if ($appointment.profilePhoneNumber) {
                        $fixes += "Patient phone: $($appointment.profilePhoneNumber)"
                    } else {
                        $issues += "Patient phone missing"
                    }
                    
                    if ($appointment.appointmentDate) {
                        $fixes += "Appointment date: $($appointment.appointmentDate)"
                    } else {
                        $issues += "Appointment date missing"
                    }
                    
                    if ($appointment.doctorName) {
                        $fixes += "Doctor name: $($appointment.doctorName)"
                    } else {
                        $issues += "Doctor name missing"
                    }
                    
                    if ($fixes.Count -gt 0) {
                        Write-Host "    ✅ Working correctly:" -ForegroundColor Green
                        foreach ($fix in $fixes) {
                            Write-Host "      - $fix" -ForegroundColor Green
                        }
                    }
                    
                    if ($issues.Count -gt 0) {
                        Write-Host "    ❌ Still need fixing:" -ForegroundColor Red
                        foreach ($issue in $issues) {
                            Write-Host "      - $issue" -ForegroundColor Red
                        }
                    }
                    
                    if ($issues.Count -eq 0) {
                        Write-Host "  🎉 ALL PATIENT INFO IS NOW DISPLAYING CORRECTLY!" -ForegroundColor Green
                        Write-Host "  🎉 The fix is working perfectly!" -ForegroundColor Green
                    }
                    
                } else {
                    Write-Host "  ⚠️ No appointments found for this doctor" -ForegroundColor Yellow
                }
                
            } catch {
                Write-Host "  ❌ Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "  ❌ Login Failed: $($loginResponse.message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== All Doctor Tests Complete ===" -ForegroundColor Green
