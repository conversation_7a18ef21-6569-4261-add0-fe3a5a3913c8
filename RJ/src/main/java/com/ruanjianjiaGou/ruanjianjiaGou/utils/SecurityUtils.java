package com.ruanjianjiaGou.ruanjianjiaGou.utils;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

@Component
public class SecurityUtils {

    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserRepository userRepository;

    /**
     * 从请求中获取当前用户ID
     */
    public Long getCurrentUserId(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            log.info("获取到Token: {}", token != null ? "存在" : "不存在");

            String phoneNumber = jwtUtils.getPhoneNumberFromToken(token);
            log.info("从Token解析出手机号: {}", phoneNumber);

            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在，手机号: " + phoneNumber));

            log.info("找到用户: ID={}, 手机号={}, 角色={}", user.getId(), user.getPhoneNumber(), user.getRole());
            return user.getId();
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
            throw e;
        }
    }

    /**
     * 从请求中获取当前用户信息
     */
    public User getCurrentUser(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            log.info("获取到Token: {}", token != null ? "存在" : "不存在");

            String phoneNumber = jwtUtils.getPhoneNumberFromToken(token);
            log.info("从Token解析出手机号: {}", phoneNumber);

            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在，手机号: " + phoneNumber));

            log.info("找到用户: ID={}, 手机号={}, 角色={}", user.getId(), user.getPhoneNumber(), user.getRole());
            return user;
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            throw e;
        }
    }

    /**
     * 从请求头获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        log.info("Authorization头: {}", bearerToken != null ? "存在" : "不存在");

        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            log.info("提取Token成功，长度: {}", token.length());
            return token;
        }

        log.error("未找到有效的认证令牌，Authorization头: {}", bearerToken);
        throw new RuntimeException("未找到有效的认证令牌");
    }
}
