# Test Diagnosis Frontend Integration

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== 诊断功能前后端集成测试 ===" -ForegroundColor Green

# 1. 医生登录
Write-Host "`n1. 医生登录测试" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-Json

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "医生登录成功" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $doctorToken"
    
    # 2. 获取预约列表
    Write-Host "`n2. 获取预约列表" -ForegroundColor Yellow
    
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my"
    $params = "?page=1&size=10"
    $fullUrl = $appointmentsUrl + $params
    
    $appointmentsResponse = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $authHeaders
    Write-Host "预约列表获取成功" -ForegroundColor Green
    Write-Host "总预约数: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        $appointment = $appointmentsResponse.data.content[0]
        
        Write-Host "`n预约详情:" -ForegroundColor White
        Write-Host "  预约ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  患者姓名: $($appointment.profileOwnerName)" -ForegroundColor Yellow
        Write-Host "  患者性别: $($appointment.profileGender)" -ForegroundColor Yellow
        Write-Host "  患者年龄: $($appointment.profileAge)" -ForegroundColor Yellow
        Write-Host "  预约状态: $($appointment.status)" -ForegroundColor Gray
        Write-Host "  诊断状态: $($appointment.diagnosisStatus)" -ForegroundColor Magenta
        Write-Host "  已诊断: $($appointment.hasDiagnosis)" -ForegroundColor Magenta
        
        # 3. 测试诊断处方创建
        Write-Host "`n3. 创建诊断处方" -ForegroundColor Yellow
        
        $prescriptionData = @{
            profileId = $appointment.profileId
            appointmentId = $appointment.id
            diagnosis = "感冒症状，建议休息并按时服药"
            medications = @(
                @{
                    name = "感冒灵颗粒"
                    specification = "10g/袋"
                    quantity = 6
                    frequency = "每日3次"
                    dosage = "每次1袋，温水冲服"
                    notes = "饭后服用，多喝水"
                },
                @{
                    name = "维生素C片"
                    specification = "100mg/片"
                    quantity = 30
                    frequency = "每日1次"
                    dosage = "每次1片"
                    notes = "增强免疫力"
                }
            )
        } | ConvertTo-Json -Depth 3
        
        try {
            $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescribe"
            $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $authHeaders
            
            Write-Host "诊断处方创建成功!" -ForegroundColor Green
            Write-Host "处方ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
            Write-Host "诊断内容: $($prescriptionResponse.data.prescription.diagnosis)" -ForegroundColor Yellow
            Write-Host "药品数量: $($prescriptionResponse.data.prescription.medications.Count)" -ForegroundColor Yellow
            
            # 4. 验证预约状态更新
            Write-Host "`n4. 验证预约状态更新" -ForegroundColor Yellow
            
            $updatedResponse = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $authHeaders
            $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $appointment.id }
            
            if ($updatedAppointment) {
                Write-Host "更新后状态:" -ForegroundColor Green
                Write-Host "  预约状态: $($updatedAppointment.status)" -ForegroundColor Gray
                Write-Host "  诊断状态: $($updatedAppointment.diagnosisStatus)" -ForegroundColor Magenta
                Write-Host "  已诊断: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                
                if ($updatedAppointment.hasDiagnosis -eq $true) {
                    Write-Host "`n✅ 诊断功能测试成功!" -ForegroundColor Green
                    Write-Host "   - 诊断处方创建成功" -ForegroundColor White
                    Write-Host "   - 预约状态自动更新" -ForegroundColor White
                    Write-Host "   - 诊断状态正确显示" -ForegroundColor White
                } else {
                    Write-Host "`n⚠️  诊断状态未正确更新" -ForegroundColor Yellow
                }
            }
            
            # 5. 获取诊断处方详情
            Write-Host "`n5. 获取诊断处方详情" -ForegroundColor Yellow
            
            try {
                $prescriptionDetailUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescription"
                $prescriptionDetailResponse = Invoke-RestMethod -Uri $prescriptionDetailUrl -Method GET -Headers $authHeaders
                
                Write-Host "处方详情获取成功:" -ForegroundColor Green
                Write-Host "  诊断: $($prescriptionDetailResponse.data.diagnosis)" -ForegroundColor Yellow
                Write-Host "  药品数量: $($prescriptionDetailResponse.data.medications.Count)" -ForegroundColor Yellow
                
                foreach ($med in $prescriptionDetailResponse.data.medications) {
                    Write-Host "    - $($med.name) ($($med.specification)) x$($med.quantity)" -ForegroundColor Cyan
                }
                
            } catch {
                Write-Host "获取处方详情失败: $($_.Exception.Message)" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "创建诊断处方失败: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "没有找到预约记录" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "测试失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

Write-Host "`n前端访问地址:" -ForegroundColor Cyan
Write-Host "- 前端地址: http://localhost:5174" -ForegroundColor White
Write-Host "- 医生预约管理: http://localhost:5174/doctor/appointments" -ForegroundColor White
Write-Host "- 测试账号: 18610001001 / 123456" -ForegroundColor White

Write-Host "`n诊断功能说明:" -ForegroundColor Yellow
Write-Host "1. 在预约列表中点击诊断按钮(🩺)" -ForegroundColor White
Write-Host "2. 填写诊断信息和药品处方" -ForegroundColor White
Write-Host "3. 提交后预约状态自动更新为已完成" -ForegroundColor White
Write-Host "4. 已诊断的预约显示查看按钮(📋)" -ForegroundColor White
