# Test Database Users and Data

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Database Users and Data ===" -ForegroundColor Green

# 1. Test a simple API to check if backend is working
Write-Host "`n1. Test Backend Health" -ForegroundColor Yellow

try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/actuator/health" -Method GET
    Write-Host "Backend Health: $($healthResponse.status)" -ForegroundColor Green
} catch {
    Write-Host "Backend Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Test user registration to see if database is working
Write-Host "`n2. Test User Registration (Database Write)" -ForegroundColor Yellow

$testPhone = "15800000999"
$registerData = @{
    phoneNumber = $testPhone
    password = "123456"
    nickname = "TestUser"
    role = "RESIDENT"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/register" -Method POST -Body $registerData -Headers $headers
    Write-Host "Registration Success: $($registerResponse.message)" -ForegroundColor Green
    
    # Try to login with the new user
    $loginData = @{
        phoneNumber = $testPhone
        password = "123456"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "New User Login Success" -ForegroundColor Green
    Write-Host "User ID: $($loginResponse.data.user.id)" -ForegroundColor Cyan
    Write-Host "Role: $($loginResponse.data.user.role)" -ForegroundColor Cyan
    Write-Host "Phone: $($loginResponse.data.user.phoneNumber)" -ForegroundColor Cyan
    
} catch {
    Write-Host "Registration/Login Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 3. Test with known working phone numbers from init-mysql.sql
Write-Host "`n3. Test Known Database Users" -ForegroundColor Yellow

$knownUsers = @(
    @{ phone = "13800000001"; role = "RESIDENT"; name = "张三" },
    @{ phone = "13800000002"; role = "RESIDENT"; name = "李四" },
    @{ phone = "13800000003"; role = "RESIDENT"; name = "王五" },
    @{ phone = "18610001001"; role = "DOCTOR"; name = "王健康" },
    @{ phone = "18610001002"; role = "DOCTOR"; name = "李医生" },
    @{ phone = "19999999999"; role = "ADMIN"; name = "管理员" }
)

foreach ($user in $knownUsers) {
    Write-Host "`nTesting $($user.role): $($user.phone) ($($user.name))" -ForegroundColor Cyan
    
    $loginData = @{
        phoneNumber = $user.phone
        password = "123456"
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        
        if ($loginResponse.code -eq 200) {
            Write-Host "  ✅ Login Success" -ForegroundColor Green
            Write-Host "  User ID: '$($loginResponse.data.user.id)'" -ForegroundColor Gray
            Write-Host "  Real Name: '$($loginResponse.data.user.realName)'" -ForegroundColor Gray
            Write-Host "  Role: '$($loginResponse.data.user.role)'" -ForegroundColor Gray
            Write-Host "  Phone: '$($loginResponse.data.user.phoneNumber)'" -ForegroundColor Gray
            Write-Host "  Nickname: '$($loginResponse.data.user.nickname)'" -ForegroundColor Gray
            
            # Check if this is a doctor and test appointments
            if ($loginResponse.data.user.role -eq "DOCTOR") {
                Write-Host "  🩺 Testing Doctor Appointments..." -ForegroundColor Magenta
                
                $token = $loginResponse.data.token
                $authHeaders = $headers.Clone()
                $authHeaders["Authorization"] = "Bearer $token"
                
                try {
                    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=5"
                    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
                    
                    Write-Host "  ✅ Doctor Appointments API Success" -ForegroundColor Green
                    Write-Host "  Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
                    
                    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                        $appointment = $appointmentsResponse.data.content[0]
                        Write-Host "  📋 First Appointment:" -ForegroundColor White
                        Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
                        Write-Host "    Patient: '$($appointment.profileOwnerName)'" -ForegroundColor Gray
                        Write-Host "    Gender: '$($appointment.profileGender)'" -ForegroundColor Gray
                        Write-Host "    Age: '$($appointment.profileAge)'" -ForegroundColor Gray
                        Write-Host "    Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Gray
                        Write-Host "    Status: '$($appointment.status)'" -ForegroundColor Gray
                        Write-Host "    Date: '$($appointment.appointmentDate)'" -ForegroundColor Gray
                        Write-Host "    Time: '$($appointment.appointmentTime)'" -ForegroundColor Gray
                        Write-Host "    Doctor: '$($appointment.doctorName)'" -ForegroundColor Gray
                        Write-Host "    Department: '$($appointment.departmentName)'" -ForegroundColor Gray
                        
                        # Check data completeness
                        $issues = @()
                        if (-not $appointment.profileOwnerName -or $appointment.profileOwnerName -eq "null") {
                            $issues += "Patient name missing"
                        }
                        if (-not $appointment.appointmentDate) {
                            $issues += "Appointment date missing"
                        }
                        if (-not $appointment.doctorName) {
                            $issues += "Doctor name missing"
                        }
                        
                        if ($issues.Count -eq 0) {
                            Write-Host "  ✅ All appointment data is complete!" -ForegroundColor Green
                        } else {
                            Write-Host "  ❌ Issues found: $($issues -join ', ')" -ForegroundColor Red
                        }
                    }
                    
                } catch {
                    Write-Host "  ❌ Doctor Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
                    if ($_.ErrorDetails.Message) {
                        Write-Host "  Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
                    }
                }
            }
            
        } else {
            Write-Host "  ❌ Login Failed: Code $($loginResponse.code), Message: $($loginResponse.message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.ErrorDetails.Message) {
            try {
                $errorResponse = $_.ErrorDetails.Message | ConvertFrom-Json
                Write-Host "  Error Code: $($errorResponse.code)" -ForegroundColor Red
                Write-Host "  Error Message: $($errorResponse.message)" -ForegroundColor Red
            } catch {
                Write-Host "  Raw Error: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host "`n=== Database User Test Complete ===" -ForegroundColor Green
