# 测试医生排班管理功能 - 调试版本
$baseUrl = "http://localhost:8080"

Write-Host "=== 医生排班管理功能调试测试 ===" -ForegroundColor Green

# 1. 医生登录
Write-Host "`n1. 医生登录测试..." -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "18610001001"
    password = "doctor666"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✅ 登录成功" -ForegroundColor Green
    Write-Host "用户信息: $($loginResponse.data.userInfo.nickname) (角色: $($loginResponse.data.userInfo.role))" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "Token: $($token.Substring(0, 20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorBody = $reader.ReadToEnd()
        Write-Host "错误详情: $errorBody" -ForegroundColor Red
    }
    exit 1
}

# 2. 获取医生排班列表
Write-Host "`n2. 获取医生排班列表..." -ForegroundColor Yellow
try {
    $scheduleResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedules/my" -Method GET -Headers $headers
    Write-Host "✅ 排班列表获取成功" -ForegroundColor Green
    Write-Host "API响应: $($scheduleResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    
    if ($scheduleResponse.data -and $scheduleResponse.data.Count -gt 0) {
        Write-Host "排班数量: $($scheduleResponse.data.Count)" -ForegroundColor Green
        foreach ($schedule in $scheduleResponse.data) {
            Write-Host "  - 日期: $($schedule.scheduleDate), 时间: $($schedule.startTime)-$($schedule.endTime), 号源: $($schedule.availableSlots)/$($schedule.totalSlots)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ 暂无排班数据" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取排班列表失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
        
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "错误详情: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取错误详情" -ForegroundColor Red
        }
    }
}

# 3. 创建测试排班
Write-Host "`n3. 创建测试排班..." -ForegroundColor Yellow
$scheduleData = @{
    scheduleDate = "2025-06-20"
    startTime = "09:00:00"
    endTime = "12:00:00"
    totalSlots = 10
} | ConvertTo-Json

try {
    $createResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedules" -Method POST -Body $scheduleData -Headers $headers
    Write-Host "✅ 排班创建成功" -ForegroundColor Green
    Write-Host "创建的排班: $($createResponse.data | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 创建排班失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
        
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "错误详情: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取错误详情" -ForegroundColor Red
        }
    }
}

# 4. 再次获取排班列表验证
Write-Host "`n4. 验证排班列表..." -ForegroundColor Yellow
try {
    $verifyResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedules/my" -Method GET -Headers $headers
    Write-Host "✅ 验证成功" -ForegroundColor Green
    Write-Host "当前排班数量: $($verifyResponse.data.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ 验证失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
