# Test Available Schedules API

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Available Schedules API ===" -ForegroundColor Green

# 1. User Login
Write-Host "`n1. User Login" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test Available Schedules for Internal Medicine
Write-Host "`n2. Test Available Schedules for Internal Medicine" -ForegroundColor Yellow

# Get tomorrow's date
$tomorrow = (Get-Date).AddDays(1).ToString("yyyy-MM-dd")
Write-Host "Testing date: $tomorrow" -ForegroundColor Cyan

try {
    $params = @{
        date = $tomorrow
        departmentId = 1  # Internal Medicine
    }
    
    $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
    $url = "$baseUrl/api/appointments/schedules/available?$queryString"
    
    Write-Host "Request URL: $url" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Message: $($response.message)" -ForegroundColor Cyan
    
    if ($response.data) {
        Write-Host "Available Schedules Count: $($response.data.Count)" -ForegroundColor Cyan
        
        if ($response.data.Count -gt 0) {
            Write-Host "`nSchedule Details:" -ForegroundColor White
            for ($i = 0; $i -lt $response.data.Count; $i++) {
                $schedule = $response.data[$i]
                Write-Host "  Schedule $($i+1):" -ForegroundColor White
                Write-Host "    ID: $($schedule.id)" -ForegroundColor Gray
                Write-Host "    Doctor: $($schedule.doctorName)" -ForegroundColor Gray
                Write-Host "    Time: $($schedule.startTime) - $($schedule.endTime)" -ForegroundColor Gray
                Write-Host "    Available Slots: $($schedule.availableSlots)" -ForegroundColor Gray
                Write-Host "    Department: $($schedule.department)" -ForegroundColor Gray
                Write-Host ""
            }
        } else {
            Write-Host "No available schedules found" -ForegroundColor Yellow
        }
        
        Write-Host "`nFull Response:" -ForegroundColor Cyan
        Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Gray
    } else {
        Write-Host "No data in response" -ForegroundColor Yellow
    }
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. Test with specific doctor
Write-Host "`n3. Test Available Schedules with Specific Doctor" -ForegroundColor Yellow

try {
    $params = @{
        date = $tomorrow
        departmentId = 1  # Internal Medicine
        doctorId = 6      # Wang Jiankang's user ID
    }
    
    $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
    $url = "$baseUrl/api/appointments/schedules/available?$queryString"
    
    Write-Host "Request URL with Doctor: $url" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "API Response Success with Doctor Filter" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Available Schedules Count: $($response.data.Count)" -ForegroundColor Cyan
    
    if ($response.data.Count -gt 0) {
        Write-Host "`nFiltered Schedule Details:" -ForegroundColor White
        foreach ($schedule in $response.data) {
            Write-Host "  - $($schedule.doctorName): $($schedule.startTime)-$($schedule.endTime) (Available: $($schedule.availableSlots))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "API Call with Doctor Filter Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test different dates
Write-Host "`n4. Test Different Dates" -ForegroundColor Yellow

$testDates = @(
    (Get-Date).ToString("yyyy-MM-dd"),           # Today
    (Get-Date).AddDays(1).ToString("yyyy-MM-dd"), # Tomorrow
    (Get-Date).AddDays(2).ToString("yyyy-MM-dd")  # Day after tomorrow
)

foreach ($testDate in $testDates) {
    Write-Host "`nTesting date: $testDate" -ForegroundColor Cyan
    
    try {
        $params = @{
            date = $testDate
            departmentId = 1
        }
        
        $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $url = "$baseUrl/api/appointments/schedules/available?$queryString"
        
        $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
        Write-Host "  Date ${testDate}: $($response.data.Count) schedules available" -ForegroundColor Green
    } catch {
        Write-Host "  Date ${testDate}: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Available Schedules Test Complete ===" -ForegroundColor Green
