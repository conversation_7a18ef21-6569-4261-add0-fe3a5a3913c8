# Test Patient Info Display Fix

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Patient Info Display Fix ===" -ForegroundColor Green

# 1. <PERSON><PERSON> as doctor (try multiple accounts)
Write-Host "`n1. Doctor Login Test" -ForegroundColor Yellow

$doctorAccounts = @(
    "***********", "***********", "***********", "***********", "***********", "***********"
)

$workingDoctor = $null
$doctorToken = $null

foreach ($phone in $doctorAccounts) {
    $loginData = @{
        phoneNumber = $phone
        password = "123456"
    } | ConvertTo-Json

    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        
        if ($loginResponse.code -eq 200 -and $loginResponse.data.token) {
            Write-Host "✅ Doctor login success: $phone" -ForegroundColor Green
            $workingDoctor = $phone
            $doctorToken = $loginResponse.data.token
            break
        }
    } catch {
        # Continue trying other accounts
    }
}

if (-not $doctorToken) {
    Write-Host "❌ No working doctor account found" -ForegroundColor Red
    exit 1
}

# 2. Test Doctor Appointments API
Write-Host "`n2. Test Doctor Appointments API" -ForegroundColor Yellow

$authHeaders = $headers.Clone()
$authHeaders["Authorization"] = "Bearer $doctorToken"

try {
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
    
    Write-Host "✅ Doctor Appointments API Success" -ForegroundColor Green
    Write-Host "Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        Write-Host "`n📋 Appointment Data Analysis:" -ForegroundColor White
        
        for ($i = 0; $i -lt [Math]::Min(3, $appointmentsResponse.data.content.Count); $i++) {
            $appointment = $appointmentsResponse.data.content[$i]
            Write-Host "`n  Appointment $($i+1):" -ForegroundColor Cyan
            Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
            Write-Host "    Status: $($appointment.status)" -ForegroundColor Gray
            
            # Patient Information Analysis
            Write-Host "    Patient Info:" -ForegroundColor Yellow
            Write-Host "      Profile ID: $($appointment.profileId)" -ForegroundColor Gray
            Write-Host "      Name: '$($appointment.profileOwnerName)'" -ForegroundColor Gray
            Write-Host "      Gender: '$($appointment.profileGender)'" -ForegroundColor Gray
            Write-Host "      Age: '$($appointment.profileAge)'" -ForegroundColor Gray
            Write-Host "      Birth Date: '$($appointment.profileBirthDate)'" -ForegroundColor Gray
            Write-Host "      Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Gray
            Write-Host "      ID Card: '$($appointment.profileIdCardNumber)'" -ForegroundColor Gray
            Write-Host "      Avatar: '$($appointment.profileAvatarUrl)'" -ForegroundColor Gray
            
            # Doctor Information Analysis
            Write-Host "    Doctor Info:" -ForegroundColor Yellow
            Write-Host "      Doctor ID: $($appointment.doctorId)" -ForegroundColor Gray
            Write-Host "      Name: '$($appointment.doctorName)'" -ForegroundColor Gray
            Write-Host "      Title: '$($appointment.doctorTitle)'" -ForegroundColor Gray
            Write-Host "      Department: '$($appointment.departmentName)'" -ForegroundColor Gray
            
            # Schedule Information Analysis
            Write-Host "    Schedule Info:" -ForegroundColor Yellow
            Write-Host "      Schedule ID: $($appointment.scheduleId)" -ForegroundColor Gray
            Write-Host "      Date: '$($appointment.appointmentDate)'" -ForegroundColor Gray
            Write-Host "      Time: '$($appointment.appointmentTime)' - '$($appointment.endTime)'" -ForegroundColor Gray
            
            # Data Completeness Check
            Write-Host "    Data Completeness:" -ForegroundColor White
            
            $checks = @(
                @{ Field = "Patient Name"; Value = $appointment.profileOwnerName; Expected = "Should not be null or empty" },
                @{ Field = "Patient Gender"; Value = $appointment.profileGender; Expected = "Should be MALE/FEMALE" },
                @{ Field = "Patient Age"; Value = $appointment.profileAge; Expected = "Should be a number" },
                @{ Field = "Patient Phone"; Value = $appointment.profilePhoneNumber; Expected = "Should be phone number" },
                @{ Field = "Doctor Name"; Value = $appointment.doctorName; Expected = "Should not be null" },
                @{ Field = "Department"; Value = $appointment.departmentName; Expected = "Should not be null" },
                @{ Field = "Appointment Date"; Value = $appointment.appointmentDate; Expected = "Should be date" },
                @{ Field = "Appointment Time"; Value = $appointment.appointmentTime; Expected = "Should be time" }
            )
            
            foreach ($check in $checks) {
                if ($check.Value -and $check.Value -ne "null" -and $check.Value -ne "") {
                    Write-Host "      ✅ $($check.Field): $($check.Value)" -ForegroundColor Green
                } else {
                    Write-Host "      ❌ $($check.Field): Missing/Null" -ForegroundColor Red
                }
            }
        }
        
        # Test individual appointment detail
        Write-Host "`n3. Test Individual Appointment Detail" -ForegroundColor Yellow
        
        $firstAppointmentId = $appointmentsResponse.data.content[0].id
        try {
            $detailUrl = "$baseUrl/api/doctor/appointments/$firstAppointmentId"
            $detailResponse = Invoke-RestMethod -Uri $detailUrl -Method GET -Headers $authHeaders
            
            Write-Host "✅ Appointment Detail API Success" -ForegroundColor Green
            $detail = $detailResponse.data
            
            Write-Host "Detail Patient Info:" -ForegroundColor White
            Write-Host "  Name: '$($detail.profileOwnerName)'" -ForegroundColor Gray
            Write-Host "  Gender: '$($detail.profileGender)'" -ForegroundColor Gray
            Write-Host "  Age: '$($detail.profileAge)'" -ForegroundColor Gray
            Write-Host "  Phone: '$($detail.profilePhoneNumber)'" -ForegroundColor Gray
            Write-Host "  Birth Date: '$($detail.profileBirthDate)'" -ForegroundColor Gray
            
        } catch {
            Write-Host "❌ Appointment Detail API Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "⚠️ No appointments found for this doctor" -ForegroundColor Yellow
        
        # Try to create a test appointment
        Write-Host "`n4. Creating Test Data" -ForegroundColor Yellow
        Write-Host "No appointments found. You may need to:" -ForegroundColor White
        Write-Host "1. Create some test appointments in the database" -ForegroundColor White
        Write-Host "2. Ensure health profiles are properly linked" -ForegroundColor White
        Write-Host "3. Verify doctor schedules exist" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Doctor Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Patient Info Display Test Complete ===" -ForegroundColor Green

# Summary of expected fixes
Write-Host "`nExpected Fixes Applied:" -ForegroundColor Cyan
Write-Host "✅ Extended AppointmentDTO with complete patient health profile fields" -ForegroundColor Green
Write-Host "✅ Updated DoctorService.convertToAppointmentDTO with full patient info mapping" -ForegroundColor Green
Write-Host "✅ Updated AppointmentService.convertToAppointmentDTO with full patient info mapping" -ForegroundColor Green
Write-Host "✅ Fixed AppointmentRepository queries with JOIN FETCH for eager loading" -ForegroundColor Green
Write-Host "✅ Added proper gender enum to string conversion" -ForegroundColor Green
Write-Host "✅ Added patient phone number from User entity" -ForegroundColor Green
Write-Host "✅ Added patient age calculation from birth date" -ForegroundColor Green
Write-Host "✅ Added patient ID card number and avatar URL" -ForegroundColor Green

Write-Host "`nFrontend should now display:" -ForegroundColor White
Write-Host "- Patient real names instead of '用户1'" -ForegroundColor Gray
Write-Host "- Patient actual age instead of '未知岁'" -ForegroundColor Gray
Write-Host "- Patient gender (男/女) instead of '未知性'" -ForegroundColor Gray
Write-Host "- Patient phone numbers" -ForegroundColor Gray
Write-Host "- Complete appointment scheduling information" -ForegroundColor Gray
