# Test Prescription-Based Diagnosis Feature

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Prescription-Based Diagnosis Feature ===" -ForegroundColor Green

# 1. <PERSON><PERSON> as approved doctor
Write-Host "`n1. Doctor Login" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-Json

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    
    if ($doctorLoginResponse.code -eq 200) {
        Write-Host "✅ Doctor Login Success" -ForegroundColor Green
        Write-Host "Doctor: $($doctorLoginResponse.data.user.nickname)" -ForegroundColor Cyan
        
        $doctorToken = $doctorLoginResponse.data.token
        $authHeaders = $headers.Clone()
        $authHeaders["Authorization"] = "Bearer $doctorToken"
        
    } else {
        Write-Host "❌ Doctor Login Failed: $($doctorLoginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Doctor Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Get doctor's appointments
Write-Host "`n2. Get Doctor Appointments" -ForegroundColor Yellow

try {
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
    
    Write-Host "✅ Appointments API Success" -ForegroundColor Green
    Write-Host "Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        $appointment = $appointmentsResponse.data.content[0]
        $appointmentId = $appointment.id
        
        Write-Host "`n📋 First Appointment Details:" -ForegroundColor White
        Write-Host "  ID: $appointmentId" -ForegroundColor Gray
        Write-Host "  Patient: $($appointment.profileOwnerName)" -ForegroundColor Yellow
        Write-Host "  Gender: $($appointment.profileGender)" -ForegroundColor Yellow
        Write-Host "  Age: $($appointment.profileAge)" -ForegroundColor Yellow
        Write-Host "  Phone: $($appointment.profilePhoneNumber)" -ForegroundColor Yellow
        Write-Host "  Status: $($appointment.status)" -ForegroundColor Green
        Write-Host "  Date: $($appointment.appointmentDate)" -ForegroundColor Green
        Write-Host "  Time: $($appointment.appointmentTime)" -ForegroundColor Green
        Write-Host "  Diagnosis Status: $($appointment.diagnosisStatus)" -ForegroundColor Magenta
        Write-Host "  Profile ID: $($appointment.profileId)" -ForegroundColor Gray
        
        # 3. Create prescription with diagnosis for this appointment
        Write-Host "`n3. Create Prescription with Diagnosis" -ForegroundColor Yellow
        
        $prescriptionData = @{
            profileId = $appointment.profileId
            appointmentId = $appointmentId
            diagnosis = "患者主诉头痛，经检查诊断为紧张性头痛。症状：头部胀痛，伴有颈部僵硬。"
            medications = @(
                @{
                    name = "布洛芬缓释胶囊"
                    specification = "0.3g"
                    quantity = 12
                    frequency = "每日2次"
                    dosage = "每次1粒，饭后服用"
                    notes = "如有胃部不适请停药"
                },
                @{
                    name = "复方氨酚烷胺片"
                    specification = "0.25g"
                    quantity = 6
                    frequency = "每日3次"
                    dosage = "每次1片，温水送服"
                    notes = "连续服用不超过3天"
                }
            )
        } | ConvertTo-Json -Depth 3
        
        try {
            $prescriptionUrl = "$baseUrl/api/doctor/appointments/$appointmentId/prescribe"
            $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $authHeaders
            
            Write-Host "✅ Prescription with Diagnosis Created Successfully" -ForegroundColor Green
            Write-Host "Prescription ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
            Write-Host "Diagnosis: $($prescriptionResponse.data.prescription.diagnosis)" -ForegroundColor Yellow
            Write-Host "Medications Count: $($prescriptionResponse.data.prescription.medications.Count)" -ForegroundColor Yellow
            
            # 4. Get prescription by appointment ID
            Write-Host "`n4. Get Prescription by Appointment ID" -ForegroundColor Yellow
            
            $getPrescriptionUrl = "$baseUrl/api/doctor/appointments/$appointmentId/prescription"
            $getPrescriptionResponse = Invoke-RestMethod -Uri $getPrescriptionUrl -Method GET -Headers $authHeaders
            
            Write-Host "✅ Get Prescription Success" -ForegroundColor Green
            $prescription = $getPrescriptionResponse.data
            Write-Host "Prescription Details:" -ForegroundColor White
            Write-Host "  ID: $($prescription.id)" -ForegroundColor Gray
            Write-Host "  Diagnosis: $($prescription.diagnosis)" -ForegroundColor Yellow
            Write-Host "  Patient: $($prescription.patientName)" -ForegroundColor Cyan
            Write-Host "  Doctor: $($prescription.doctorName)" -ForegroundColor Cyan
            Write-Host "  Department: $($prescription.departmentName)" -ForegroundColor Cyan
            Write-Host "  Created: $($prescription.createdAt)" -ForegroundColor Gray
            
            Write-Host "  Medications:" -ForegroundColor Yellow
            foreach ($med in $prescription.medications) {
                Write-Host "    - $($med.name) $($med.specification)" -ForegroundColor Gray
                Write-Host "      数量: $($med.quantity), 频次: $($med.frequency)" -ForegroundColor Gray
                Write-Host "      用法: $($med.dosage)" -ForegroundColor Gray
                if ($med.notes) {
                    Write-Host "      备注: $($med.notes)" -ForegroundColor Gray
                }
            }
            
            # 5. Check updated appointment status
            Write-Host "`n5. Check Updated Appointment Status" -ForegroundColor Yellow
            
            $updatedAppointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
            $updatedAppointment = $updatedAppointmentsResponse.data.content | Where-Object { $_.id -eq $appointmentId }
            
            if ($updatedAppointment) {
                Write-Host "✅ Appointment Status Updated" -ForegroundColor Green
                Write-Host "  Status: $($updatedAppointment.status)" -ForegroundColor Green
                Write-Host "  Diagnosis Status: $($updatedAppointment.diagnosisStatus)" -ForegroundColor Magenta
                Write-Host "  Has Diagnosis: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                
                if ($updatedAppointment.hasDiagnosis -eq $true -and $updatedAppointment.diagnosisStatus -eq "已诊断") {
                    Write-Host "`n🎉 PRESCRIPTION-BASED DIAGNOSIS WORKING PERFECTLY!" -ForegroundColor Green
                    Write-Host "🎉 Appointment now shows diagnosis status correctly!" -ForegroundColor Green
                    Write-Host "🎉 Appointment status updated to COMPLETED!" -ForegroundColor Green
                } else {
                    Write-Host "`n⚠️ Diagnosis status not updated correctly" -ForegroundColor Yellow
                }
            }
            
            # 6. Get doctor's prescription list
            Write-Host "`n6. Get Doctor's Prescription List" -ForegroundColor Yellow
            
            $prescriptionListUrl = "$baseUrl/api/doctor/prescriptions/my?page=1&size=10"
            $prescriptionListResponse = Invoke-RestMethod -Uri $prescriptionListUrl -Method GET -Headers $authHeaders
            
            Write-Host "✅ Prescription List Retrieved" -ForegroundColor Green
            Write-Host "Total Prescriptions: $($prescriptionListResponse.data.totalElements)" -ForegroundColor Cyan
            
            if ($prescriptionListResponse.data.content -and $prescriptionListResponse.data.content.Count -gt 0) {
                Write-Host "Recent Prescriptions:" -ForegroundColor White
                foreach ($presc in $prescriptionListResponse.data.content) {
                    Write-Host "  - Patient: $($presc.patientName), Diagnosis: $($presc.diagnosis)" -ForegroundColor Gray
                    Write-Host "    Created: $($presc.createdAt)" -ForegroundColor Gray
                }
            }
            
        } catch {
            Write-Host "❌ Prescription Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "⚠️ No appointments found for this doctor" -ForegroundColor Yellow
        Write-Host "Cannot test prescription-based diagnosis without appointments" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Prescription-Based Diagnosis Test Complete ===" -ForegroundColor Green

Write-Host "`nPrescription-Based Diagnosis Feature Summary:" -ForegroundColor Cyan
Write-Host "✅ Uses existing e_prescriptions table with diagnosis field" -ForegroundColor Green
Write-Host "✅ Doctors can create prescription with diagnosis for any appointment" -ForegroundColor Green
Write-Host "✅ No time restrictions - diagnose anytime" -ForegroundColor Green
Write-Host "✅ Comprehensive diagnosis + medication information" -ForegroundColor Green
Write-Host "✅ Appointment status automatically updated to 'COMPLETED' after prescription" -ForegroundColor Green
Write-Host "✅ Diagnosis status shown in appointment list (已诊断/未诊断)" -ForegroundColor Green
Write-Host "✅ Doctors can view their prescription/diagnosis history" -ForegroundColor Green
Write-Host "✅ Integrates with existing online consultation workflow" -ForegroundColor Green

Write-Host "`nAPI Endpoints:" -ForegroundColor White
Write-Host "- POST /api/doctor/appointments/{id}/prescribe - Create prescription with diagnosis" -ForegroundColor Gray
Write-Host "- GET /api/doctor/appointments/{id}/prescription - Get prescription by appointment" -ForegroundColor Gray
Write-Host "- GET /api/doctor/prescriptions/my - Get doctor's prescription list" -ForegroundColor Gray

Write-Host "`nWorkflow Integration:" -ForegroundColor White
Write-Host "- Online Consultation → Messages → Prescription with Diagnosis" -ForegroundColor Gray
Write-Host "- Offline Appointment → Direct Prescription with Diagnosis" -ForegroundColor Gray
Write-Host "- Both workflows use same e_prescriptions table" -ForegroundColor Gray
