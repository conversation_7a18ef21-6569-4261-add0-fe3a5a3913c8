# Doctor Appointment Management Test - Simplified

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Doctor Appointment Management Test ===" -ForegroundColor Green

# 1. Doctor Login
Write-Host "`n1. Doctor Login Test" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13900000002"
    password = "123456"
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success" -ForegroundColor Green
    Write-Host "User: $($loginResponse.data.userInfo.realName) - Doctor" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
    $doctorUserId = $loginResponse.data.userInfo.id
    
    Write-Host "Doctor User ID: $doctorUserId" -ForegroundColor Gray
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Get Doctor Appointments
Write-Host "`n2. Get Doctor Appointments" -ForegroundColor Yellow
try {
    $appointmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=10" -Method GET -Headers $headers
    Write-Host "Get Appointments Success" -ForegroundColor Green
    Write-Host "Total: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Current Page: $($appointmentsResponse.data.content.Count)" -ForegroundColor Cyan
    
    $appointments = $appointmentsResponse.data.content
    
    if ($appointments.Count -gt 0) {
        Write-Host "`nAppointment Details:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $appointments.Count; $i++) {
            $appointment = $appointments[$i]
            Write-Host "  [$($i+1)] ID: $($appointment.id)" -ForegroundColor White
            Write-Host "      Patient: $($appointment.profileOwnerName)" -ForegroundColor White
            Write-Host "      Status: $($appointment.status)" -ForegroundColor White
            Write-Host "      Date: $($appointment.appointmentDate)" -ForegroundColor White
            Write-Host "      Time: $($appointment.appointmentTime)" -ForegroundColor White
            Write-Host ""
        }
    } else {
        Write-Host "No appointments found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Get Appointments Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. Test Status Filtering
Write-Host "`n3. Test Status Filtering" -ForegroundColor Yellow
$statusFilters = @("BOOKED", "COMPLETED", "CANCELLED")

foreach ($status in $statusFilters) {
    try {
        $filteredResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=10&status=$status" -Method GET -Headers $headers
        Write-Host "Status Filter [$status] Success: $($filteredResponse.data.totalElements) records" -ForegroundColor Green
    } catch {
        Write-Host "Status Filter [$status] Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. Test Appointment Details
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n4. Test Appointment Details" -ForegroundColor Yellow
    $firstAppointment = $appointments[0]
    
    try {
        $detailResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($firstAppointment.id)" -Method GET -Headers $headers
        Write-Host "Get Appointment Details Success" -ForegroundColor Green
        Write-Host "Appointment ID: $($detailResponse.data.id)" -ForegroundColor Cyan
        Write-Host "Patient: $($detailResponse.data.profileOwnerName)" -ForegroundColor Cyan
        Write-Host "Status: $($detailResponse.data.status)" -ForegroundColor Cyan
    } catch {
        Write-Host "Get Appointment Details Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. Test Appointment Operations
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n5. Test Appointment Operations" -ForegroundColor Yellow
    
    # Find BOOKED appointment
    $bookedAppointment = $appointments | Where-Object { $_.status -eq "BOOKED" } | Select-Object -First 1
    
    if ($bookedAppointment) {
        Write-Host "`n5.1 Test Confirm Appointment" -ForegroundColor Cyan
        try {
            $confirmResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/confirm" -Method POST -Headers $headers
            Write-Host "Confirm Success: $($confirmResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "Confirm Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n5.2 Test Complete Appointment" -ForegroundColor Cyan
        $completeData = @{
            notes = "Treatment completed successfully"
        } | ConvertTo-Json
        
        try {
            $completeResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/complete" -Method POST -Body $completeData -Headers $headers
            Write-Host "Complete Success: $($completeResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "Complete Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "No BOOKED appointments found for testing" -ForegroundColor Yellow
    }
}

# 6. Test Appointment Statistics
Write-Host "`n6. Test Appointment Statistics" -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/stats" -Method GET -Headers $headers
    Write-Host "Get Statistics Success" -ForegroundColor Green
    Write-Host "Statistics Data: $($statsResponse.data)" -ForegroundColor Cyan
} catch {
    Write-Host "Get Statistics Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. Test Doctor Schedule
Write-Host "`n7. Test Doctor Schedule" -ForegroundColor Yellow
try {
    $scheduleResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedule/my" -Method GET -Headers $headers
    Write-Host "Get Schedule Success" -ForegroundColor Green
    Write-Host "Schedule Count: $($scheduleResponse.data.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "Get Schedule Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. Test Doctor Profile
Write-Host "`n8. Test Doctor Profile" -ForegroundColor Yellow
try {
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/profile" -Method GET -Headers $headers
    Write-Host "Get Profile Success" -ForegroundColor Green
    Write-Host "Name: $($profileResponse.data.realName)" -ForegroundColor Cyan
    Write-Host "Phone: $($profileResponse.data.phoneNumber)" -ForegroundColor Cyan
    Write-Host "Role: $($profileResponse.data.role)" -ForegroundColor Cyan
} catch {
    Write-Host "Get Profile Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Doctor Appointment Management Test Complete ===" -ForegroundColor Green
