-- 为医生预约管理功能添加测试数据

-- 首先查看现有的医生和患者数据
SELECT 'Current Doctors:' as info;
SELECT id, user_id, real_name, department_id, status FROM doctors WHERE status = 'APPROVED' LIMIT 5;

SELECT 'Current Health Profiles:' as info;
SELECT id, user_id, real_name FROM health_profiles LIMIT 5;

-- 为测试医生(user_id=14)添加预约数据
-- 假设已有健康档案，如果没有则先创建

-- 插入测试预约数据
INSERT INTO appointments (
    patient_profile_id, 
    doctor_id, 
    appointment_date, 
    appointment_time, 
    status, 
    reason,
    created_at,
    updated_at
) VALUES 
-- 已预约状态的预约
(1, 14, '2025-06-17', '09:00:00', 'BOOKED', '定期健康检查', NOW(), NOW()),
(2, 14, '2025-06-17', '10:00:00', 'BOOKED', '感冒症状咨询', NOW(), NOW()),
(3, 14, '2025-06-17', '14:00:00', 'BOOKED', '血压监测', NOW(), NOW()),

-- 已完成状态的预约
(1, 14, '2025-06-16', '09:00:00', 'COMPLETED', '体检复查', NOW(), NOW()),
(2, 14, '2025-06-16', '15:00:00', 'COMPLETED', '药物咨询', NOW(), NOW()),

-- 已取消状态的预约
(3, 14, '2025-06-16', '11:00:00', 'CANCELLED', '时间冲突取消', NOW(), NOW());

-- 为医生添加排班数据
INSERT INTO doctor_schedules (
    doctor_id,
    day_of_week,
    start_time,
    end_time,
    max_appointments,
    created_at,
    updated_at
) VALUES 
-- 周一到周五的排班
(14, 1, '08:00:00', '12:00:00', 8, NOW(), NOW()),  -- 周一上午
(14, 1, '14:00:00', '18:00:00', 8, NOW(), NOW()),  -- 周一下午
(14, 2, '08:00:00', '12:00:00', 8, NOW(), NOW()),  -- 周二上午
(14, 2, '14:00:00', '18:00:00', 8, NOW(), NOW()),  -- 周二下午
(14, 3, '08:00:00', '12:00:00', 8, NOW(), NOW()),  -- 周三上午
(14, 3, '14:00:00', '18:00:00', 8, NOW(), NOW()),  -- 周三下午
(14, 4, '08:00:00', '12:00:00', 8, NOW(), NOW()),  -- 周四上午
(14, 4, '14:00:00', '18:00:00', 8, NOW(), NOW()),  -- 周四下午
(14, 5, '08:00:00', '12:00:00', 8, NOW(), NOW()),  -- 周五上午
(14, 5, '14:00:00', '18:00:00', 8, NOW(), NOW());  -- 周五下午

-- 检查插入的数据
SELECT 'Test Appointments Added:' as info;
SELECT 
    a.id,
    a.patient_profile_id,
    a.doctor_id,
    a.appointment_date,
    a.appointment_time,
    a.status,
    a.reason
FROM appointments a 
WHERE a.doctor_id = 14 
ORDER BY a.appointment_date DESC, a.appointment_time;

SELECT 'Doctor Schedules Added:' as info;
SELECT 
    ds.id,
    ds.doctor_id,
    ds.day_of_week,
    ds.start_time,
    ds.end_time,
    ds.max_appointments
FROM doctor_schedules ds 
WHERE ds.doctor_id = 14 
ORDER BY ds.day_of_week, ds.start_time;
