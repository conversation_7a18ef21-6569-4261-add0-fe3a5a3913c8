# 医生预约管理功能完整测试脚本

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== 医生预约管理功能完整测试 ===" -ForegroundColor Green

# 1. 医生登录
Write-Host "`n1. 医生登录测试" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13900000002"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "✅ 医生登录成功" -ForegroundColor Green
    Write-Host "用户信息: $($loginResponse.data.userInfo.realName) - 医生" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
    $doctorUserId = $loginResponse.data.userInfo.id
    
    Write-Host "医生用户ID: $doctorUserId" -ForegroundColor Gray
} catch {
    Write-Host "❌ 医生登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 获取医生的预约患者列表
Write-Host "`n2. 获取医生预约患者列表" -ForegroundColor Yellow
try {
    $appointmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=10" -Method GET -Headers $headers
    Write-Host "✅ 获取预约列表成功" -ForegroundColor Green
    Write-Host "预约总数: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "当前页预约数: $($appointmentsResponse.data.content.Count)" -ForegroundColor Cyan
    
    $appointments = $appointmentsResponse.data.content
    
    if ($appointments.Count -gt 0) {
        Write-Host "`n预约列表详情:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $appointments.Count; $i++) {
            $appointment = $appointments[$i]
            Write-Host "  [$($i+1)] 预约ID: $($appointment.id)" -ForegroundColor White
            Write-Host "      患者: $($appointment.profileOwnerName)" -ForegroundColor White
            Write-Host "      状态: $($appointment.status)" -ForegroundColor White
            Write-Host "      日期: $($appointment.appointmentDate)" -ForegroundColor White
            Write-Host "      时间: $($appointment.appointmentTime)" -ForegroundColor White
            Write-Host "      原因: $($appointment.reason)" -ForegroundColor White
            Write-Host ""
        }
    } else {
        Write-Host "⚠️ 暂无预约记录" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取预约列表失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. 测试预约状态筛选
Write-Host "`n3. 测试预约状态筛选" -ForegroundColor Yellow
$statusFilters = @("BOOKED", "COMPLETED", "CANCELLED")

foreach ($status in $statusFilters) {
    try {
        $filteredResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=10&status=$status" -Method GET -Headers $headers
        Write-Host "✅ 状态筛选 [$status] 成功: $($filteredResponse.data.totalElements) 条记录" -ForegroundColor Green
    } catch {
        Write-Host "❌ 状态筛选 [$status] 失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 测试预约详情查询
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n4. 测试预约详情查询" -ForegroundColor Yellow
    $firstAppointment = $appointments[0]
    
    try {
        $detailResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($firstAppointment.id)" -Method GET -Headers $headers
        Write-Host "✅ 获取预约详情成功" -ForegroundColor Green
        Write-Host "预约详情:" -ForegroundColor Cyan
        Write-Host "  预约ID: $($detailResponse.data.id)" -ForegroundColor White
        Write-Host "  患者姓名: $($detailResponse.data.profileOwnerName)" -ForegroundColor White
        Write-Host "  预约状态: $($detailResponse.data.status)" -ForegroundColor White
        Write-Host "  预约时间: $($detailResponse.data.appointmentDate) $($detailResponse.data.appointmentTime)" -ForegroundColor White
    } catch {
        Write-Host "❌ 获取预约详情失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 测试预约操作（如果有可操作的预约）
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n5. 测试预约操作" -ForegroundColor Yellow
    
    # 查找可确认的预约
    $bookedAppointment = $appointments | Where-Object { $_.status -eq "BOOKED" } | Select-Object -First 1
    
    if ($bookedAppointment) {
        Write-Host "`n5.1 测试确认预约" -ForegroundColor Cyan
        try {
            $confirmResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/confirm" -Method POST -Headers $headers
            Write-Host "✅ 确认预约成功: $($confirmResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ 确认预约失败: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
        }
        
        Write-Host "`n5.2 测试完成诊疗" -ForegroundColor Cyan
        $completeData = @{
            notes = "诊疗完成，患者恢复良好，建议定期复查"
        } | ConvertTo-Json
        
        try {
            $completeResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($bookedAppointment.id)/complete" -Method POST -Body $completeData -Headers $headers
            Write-Host "✅ 完成诊疗成功: $($completeResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ 完成诊疗失败: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ 没有找到可操作的预约（状态为BOOKED）" -ForegroundColor Yellow
    }
}

# 6. 测试预约统计信息
Write-Host "`n6. 测试预约统计信息" -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/stats" -Method GET -Headers $headers
    Write-Host "✅ 获取预约统计成功" -ForegroundColor Green
    Write-Host "统计信息:" -ForegroundColor Cyan
    if ($statsResponse.data) {
        Write-Host "  统计数据: $($statsResponse.data | ConvertTo-Json -Compress)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ 获取预约统计失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 7. 测试医生排班查询
Write-Host "`n7. 测试医生排班查询" -ForegroundColor Yellow
try {
    $scheduleResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedule/my" -Method GET -Headers $headers
    Write-Host "✅ 获取医生排班成功" -ForegroundColor Green
    Write-Host "排班数量: $($scheduleResponse.data.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 获取医生排班失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 8. 测试医生个人信息查询
Write-Host "`n8. 测试医生个人信息查询" -ForegroundColor Yellow
try {
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/profile" -Method GET -Headers $headers
    Write-Host "✅ 获取医生个人信息成功" -ForegroundColor Green
    Write-Host "医生信息:" -ForegroundColor Cyan
    Write-Host "  姓名: $($profileResponse.data.realName)" -ForegroundColor White
    Write-Host "  电话: $($profileResponse.data.phoneNumber)" -ForegroundColor White
    Write-Host "  角色: $($profileResponse.data.role)" -ForegroundColor White
} catch {
    Write-Host "❌ 获取医生个人信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 医生预约管理功能测试完成 ===" -ForegroundColor Green
Write-Host "测试总结:" -ForegroundColor Cyan
Write-Host "✅ 医生登录" -ForegroundColor Green
Write-Host "✅ 获取预约列表" -ForegroundColor Green
Write-Host "✅ 状态筛选" -ForegroundColor Green
Write-Host "✅ 预约详情查询" -ForegroundColor Green
Write-Host "✅ 预约操作（确认、完成）" -ForegroundColor Green
Write-Host "✅ 预约统计" -ForegroundColor Green
Write-Host "✅ 医生排班查询" -ForegroundColor Green
Write-Host "✅ 医生个人信息查询" -ForegroundColor Green
