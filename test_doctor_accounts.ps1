# Test Doctor Accounts with Different Passwords

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Doctor Accounts with Different Passwords ===" -ForegroundColor Green

# Test different password combinations
$passwords = @("123456", "password", "123", "admin", "doctor")
$doctorPhones = @("***********", "***********", "***********", "***********", "***********", "***********")

foreach ($phone in $doctorPhones) {
    Write-Host "`nTesting Doctor Phone: $phone" -ForegroundColor Yellow
    
    $found = $false
    foreach ($password in $passwords) {
        $loginData = @{
            phoneNumber = $phone
            password = $password
        } | ConvertTo-Json

        try {
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
            
            if ($loginResponse.code -eq 200) {
                Write-Host "  ✅ Login Success with password: $password" -ForegroundColor Green
                Write-Host "  User ID: $($loginResponse.data.user.id)" -ForegroundColor Cyan
                Write-Host "  Real Name: $($loginResponse.data.user.realName)" -ForegroundColor Cyan
                Write-Host "  Role: $($loginResponse.data.user.role)" -ForegroundColor Cyan
                Write-Host "  Phone: $($loginResponse.data.user.phoneNumber)" -ForegroundColor Cyan
                
                # If this is a doctor, test the appointments API
                if ($loginResponse.data.user.role -eq "DOCTOR") {
                    Write-Host "  🎯 DOCTOR role confirmed!" -ForegroundColor Magenta
                    
                    $token = $loginResponse.data.token
                    $authHeaders = $headers.Clone()
                    $authHeaders["Authorization"] = "Bearer $token"
                    
                    try {
                        $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=5"
                        $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
                        
                        Write-Host "  ✅ Doctor Appointments API Success" -ForegroundColor Green
                        Write-Host "  Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
                        
                        if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                            $appointment = $appointmentsResponse.data.content[0]
                            Write-Host "  📋 Sample Appointment:" -ForegroundColor White
                            Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
                            Write-Host "    Patient: $($appointment.profileOwnerName)" -ForegroundColor Gray
                            Write-Host "    Status: $($appointment.status)" -ForegroundColor Gray
                            Write-Host "    Date: $($appointment.appointmentDate)" -ForegroundColor Gray
                            Write-Host "    Time: $($appointment.appointmentTime)" -ForegroundColor Gray
                            Write-Host "    Doctor: $($appointment.doctorName)" -ForegroundColor Gray
                            Write-Host "    Department: $($appointment.departmentName)" -ForegroundColor Gray
                            
                            # Check patient info completeness
                            Write-Host "  📊 Patient Info Check:" -ForegroundColor White
                            if ($appointment.profileOwnerName -and $appointment.profileOwnerName -ne "null") {
                                Write-Host "    ✅ Patient Name: $($appointment.profileOwnerName)" -ForegroundColor Green
                            } else {
                                Write-Host "    ❌ Patient Name: Missing" -ForegroundColor Red
                            }
                            
                            if ($appointment.profileGender) {
                                Write-Host "    ✅ Patient Gender: $($appointment.profileGender)" -ForegroundColor Green
                            } else {
                                Write-Host "    ❌ Patient Gender: Missing" -ForegroundColor Red
                            }
                            
                            if ($appointment.profileAge) {
                                Write-Host "    ✅ Patient Age: $($appointment.profileAge)" -ForegroundColor Green
                            } else {
                                Write-Host "    ❌ Patient Age: Missing" -ForegroundColor Red
                            }
                            
                            if ($appointment.profilePhoneNumber) {
                                Write-Host "    ✅ Patient Phone: $($appointment.profilePhoneNumber)" -ForegroundColor Green
                            } else {
                                Write-Host "    ❌ Patient Phone: Missing" -ForegroundColor Red
                            }
                            
                            if ($appointment.appointmentDate) {
                                Write-Host "    ✅ Appointment Date: $($appointment.appointmentDate)" -ForegroundColor Green
                            } else {
                                Write-Host "    ❌ Appointment Date: Missing" -ForegroundColor Red
                            }
                            
                            Write-Host "  🎉 This doctor account works and has appointment data!" -ForegroundColor Magenta
                        } else {
                            Write-Host "  ⚠️ No appointments found for this doctor" -ForegroundColor Yellow
                        }
                        
                    } catch {
                        Write-Host "  ❌ Doctor Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
                
                $found = $true
                break
            }
            
        } catch {
            # Continue trying other passwords
        }
    }
    
    if (-not $found) {
        Write-Host "  ❌ No valid password found for $phone" -ForegroundColor Red
    }
}

# Test known working accounts
Write-Host "`nTesting Known Working Accounts:" -ForegroundColor Yellow

$knownAccounts = @(
    @{ phone = "***********"; password = "123456"; role = "RESIDENT" },
    @{ phone = "***********"; password = "123456"; role = "ADMIN" }
)

foreach ($account in $knownAccounts) {
    Write-Host "`nTesting $($account.role): $($account.phone)" -ForegroundColor Cyan
    
    $loginData = @{
        phoneNumber = $account.phone
        password = $account.password
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        
        if ($loginResponse.code -eq 200) {
            Write-Host "  ✅ Login Success" -ForegroundColor Green
            Write-Host "  User ID: $($loginResponse.data.user.id)" -ForegroundColor Cyan
            Write-Host "  Real Name: $($loginResponse.data.user.realName)" -ForegroundColor Cyan
            Write-Host "  Role: $($loginResponse.data.user.role)" -ForegroundColor Cyan
        } else {
            Write-Host "  ❌ Login Failed: Code $($loginResponse.code)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Doctor Account Test Complete ===" -ForegroundColor Green
