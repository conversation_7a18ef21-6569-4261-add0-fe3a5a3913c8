# Test Department Doctors API

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Department Doctors API ===" -ForegroundColor Green

# 1. User Login
Write-Host "`n1. User Login" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test Internal Medicine Department (ID: 1)
Write-Host "`n2. Test Internal Medicine Department (ID: 1)" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/1/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Structure:" -ForegroundColor Cyan
    Write-Host "  Code: $($response.code)" -ForegroundColor White
    Write-Host "  Message: $($response.message)" -ForegroundColor White
    Write-Host "  Data Type: $($response.data.GetType().Name)" -ForegroundColor White
    
    if ($response.data) {
        Write-Host "  Data Properties:" -ForegroundColor White
        $response.data | Get-Member -MemberType Properties | ForEach-Object {
            Write-Host "    - $($_.Name): $($response.data.($_.Name))" -ForegroundColor Gray
        }
        
        if ($response.data.content) {
            Write-Host "`nDoctors in Content:" -ForegroundColor Cyan
            Write-Host "  Content Count: $($response.data.content.Count)" -ForegroundColor White
            
            if ($response.data.content.Count -gt 0) {
                for ($i = 0; $i -lt $response.data.content.Count; $i++) {
                    $doctor = $response.data.content[$i]
                    Write-Host "  Doctor $($i+1):" -ForegroundColor White
                    Write-Host "    Name: $($doctor.realName)" -ForegroundColor Gray
                    Write-Host "    UserId: $($doctor.userId)" -ForegroundColor Gray
                    Write-Host "    Title: $($doctor.title)" -ForegroundColor Gray
                    Write-Host "    Status: $($doctor.status)" -ForegroundColor Gray
                    Write-Host "    DepartmentId: $($doctor.departmentId)" -ForegroundColor Gray
                }
            }
        }
    }
    
    Write-Host "`nFull Response JSON:" -ForegroundColor Cyan
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Gray
    
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. Test Pediatrics Department (ID: 3)
Write-Host "`n3. Test Pediatrics Department (ID: 3)" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/3/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Total Elements: $($response.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Content Count: $($response.data.content.Count)" -ForegroundColor Cyan
    
    if ($response.data.content.Count -gt 0) {
        Write-Host "Pediatrics Doctors:" -ForegroundColor White
        foreach ($doctor in $response.data.content) {
            Write-Host "  - $($doctor.realName) | Title: $($doctor.title)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test Surgery Department (ID: 2)
Write-Host "`n4. Test Surgery Department (ID: 2)" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/2/doctors?page=1&size=20" -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Total Elements: $($response.data.totalElements)" -ForegroundColor Cyan
    Write-Host "Content Count: $($response.data.content.Count)" -ForegroundColor Cyan
    
    if ($response.data.content.Count -gt 0) {
        Write-Host "Surgery Doctors:" -ForegroundColor White
        foreach ($doctor in $response.data.content) {
            Write-Host "  - $($doctor.realName) | Title: $($doctor.title)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
