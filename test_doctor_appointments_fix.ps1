# Test Doctor Appointments Fix

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test Doctor Appointments Patient Info Fix ===" -ForegroundColor Green

# 1. Doctor Login
Write-Host "`n1. Doctor Login" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    Write-Host "Doctor: $($loginResponse.data.user.realName)" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Doctor Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Get Doctor Appointments
Write-Host "`n2. Get Doctor Appointments with Patient Info" -ForegroundColor Yellow

try {
    $params = @{
        page = 1
        size = 10
    }
    
    $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
    $url = "$baseUrl/api/doctor/appointments/my?$queryString"
    
    Write-Host "Request URL: $url" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Total Appointments: $($response.data.totalElements)" -ForegroundColor Cyan
    
    if ($response.data.content -and $response.data.content.Count -gt 0) {
        Write-Host "`nAppointment Details:" -ForegroundColor White
        
        for ($i = 0; $i -lt [Math]::Min(3, $response.data.content.Count); $i++) {
            $appointment = $response.data.content[$i]
            Write-Host "`n  Appointment $($i+1):" -ForegroundColor White
            Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
            Write-Host "    Status: $($appointment.status) ($($appointment.statusDescription))" -ForegroundColor Gray
            Write-Host "    Created: $($appointment.createdAt)" -ForegroundColor Gray
            
            # Patient Information
            Write-Host "    Patient Info:" -ForegroundColor Yellow
            Write-Host "      Profile ID: $($appointment.profileId)" -ForegroundColor Gray
            Write-Host "      Name: $($appointment.profileOwnerName)" -ForegroundColor Gray
            Write-Host "      Gender: $($appointment.profileGender)" -ForegroundColor Gray
            Write-Host "      Birth Date: $($appointment.profileBirthDate)" -ForegroundColor Gray
            Write-Host "      Age: $($appointment.profileAge)" -ForegroundColor Gray
            Write-Host "      Phone: $($appointment.profilePhoneNumber)" -ForegroundColor Gray
            Write-Host "      ID Card: $($appointment.profileIdCardNumber)" -ForegroundColor Gray
            
            # Doctor Information
            Write-Host "    Doctor Info:" -ForegroundColor Yellow
            Write-Host "      Doctor ID: $($appointment.doctorId)" -ForegroundColor Gray
            Write-Host "      Name: $($appointment.doctorName)" -ForegroundColor Gray
            Write-Host "      Title: $($appointment.doctorTitle)" -ForegroundColor Gray
            Write-Host "      Department: $($appointment.departmentName)" -ForegroundColor Gray
            
            # Schedule Information
            Write-Host "    Schedule Info:" -ForegroundColor Yellow
            Write-Host "      Schedule ID: $($appointment.scheduleId)" -ForegroundColor Gray
            Write-Host "      Date: $($appointment.appointmentDate)" -ForegroundColor Gray
            Write-Host "      Time: $($appointment.appointmentTime) - $($appointment.endTime)" -ForegroundColor Gray
            
            # Notes
            if ($appointment.notes) {
                Write-Host "    Notes: $($appointment.notes)" -ForegroundColor Gray
            }
            
            Write-Host ""
        }
        
        # Check for missing information
        Write-Host "`nData Completeness Check:" -ForegroundColor Cyan
        $firstAppointment = $response.data.content[0]
        
        $checks = @(
            @{ Field = "profileOwnerName"; Value = $firstAppointment.profileOwnerName; Expected = "Patient name should not be null" },
            @{ Field = "profileGender"; Value = $firstAppointment.profileGender; Expected = "Gender should be MALE/FEMALE" },
            @{ Field = "profileAge"; Value = $firstAppointment.profileAge; Expected = "Age should be a number" },
            @{ Field = "profilePhoneNumber"; Value = $firstAppointment.profilePhoneNumber; Expected = "Phone number should not be null" },
            @{ Field = "doctorName"; Value = $firstAppointment.doctorName; Expected = "Doctor name should not be null" },
            @{ Field = "departmentName"; Value = $firstAppointment.departmentName; Expected = "Department name should not be null" },
            @{ Field = "appointmentDate"; Value = $firstAppointment.appointmentDate; Expected = "Appointment date should not be null" },
            @{ Field = "appointmentTime"; Value = $firstAppointment.appointmentTime; Expected = "Appointment time should not be null" }
        )
        
        foreach ($check in $checks) {
            if ($check.Value -and $check.Value -ne "null" -and $check.Value -ne "") {
                Write-Host "  ✅ $($check.Field): $($check.Value)" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($check.Field): Missing or null" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "No appointments found for this doctor" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. Test Appointment Detail
Write-Host "`n3. Test Individual Appointment Detail" -ForegroundColor Yellow

try {
    # Get first appointment ID from previous response
    if ($response.data.content -and $response.data.content.Count -gt 0) {
        $appointmentId = $response.data.content[0].id
        $detailUrl = "$baseUrl/api/doctor/appointments/$appointmentId"
        
        Write-Host "Getting appointment detail for ID: $appointmentId" -ForegroundColor Cyan
        
        $detailResponse = Invoke-RestMethod -Uri $detailUrl -Method GET -Headers $headers
        Write-Host "Appointment Detail API Success" -ForegroundColor Green
        
        $detail = $detailResponse.data
        Write-Host "`nDetailed Patient Information:" -ForegroundColor White
        Write-Host "  Name: $($detail.profileOwnerName)" -ForegroundColor Gray
        Write-Host "  Gender: $($detail.profileGender)" -ForegroundColor Gray
        Write-Host "  Age: $($detail.profileAge)" -ForegroundColor Gray
        Write-Host "  Phone: $($detail.profilePhoneNumber)" -ForegroundColor Gray
        Write-Host "  Birth Date: $($detail.profileBirthDate)" -ForegroundColor Gray
        Write-Host "  ID Card: $($detail.profileIdCardNumber)" -ForegroundColor Gray
        
    } else {
        Write-Host "No appointments available for detail test" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Appointment Detail API Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test Different Status Filters
Write-Host "`n4. Test Status Filters" -ForegroundColor Yellow

$statuses = @("BOOKED", "COMPLETED", "CANCELLED")

foreach ($status in $statuses) {
    Write-Host "`nTesting status filter: $status" -ForegroundColor Cyan
    
    try {
        $params = @{
            page = 1
            size = 5
            status = $status
        }
        
        $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $url = "$baseUrl/api/doctor/appointments/my?$queryString"
        
        $statusResponse = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
        Write-Host "  Status ${status}: $($statusResponse.data.totalElements) appointments" -ForegroundColor Green
        
        if ($statusResponse.data.content -and $statusResponse.data.content.Count -gt 0) {
            $sample = $statusResponse.data.content[0]
            Write-Host "  Sample: $($sample.profileOwnerName) - $($sample.status)" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "  Status ${status}: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Doctor Appointments Patient Info Fix Test Complete ===" -ForegroundColor Green

# 5. Summary
Write-Host "`n5. Fix Summary" -ForegroundColor Yellow
Write-Host "✅ Extended AppointmentDTO with patient health profile fields" -ForegroundColor Green
Write-Host "✅ Updated DoctorService.convertToAppointmentDTO method" -ForegroundColor Green
Write-Host "✅ Updated AppointmentService.convertToAppointmentDTO method" -ForegroundColor Green
Write-Host "✅ Fixed AppointmentRepository queries with JOIN FETCH" -ForegroundColor Green
Write-Host "✅ Added proper gender mapping from enum to string" -ForegroundColor Green
Write-Host "✅ Added patient phone number from User entity" -ForegroundColor Green

Write-Host "`nExpected Improvements:" -ForegroundColor Cyan
Write-Host "- Patient names should display correctly (not '未知患者')" -ForegroundColor White
Write-Host "- Patient gender, age, phone number should be visible" -ForegroundColor White
Write-Host "- Appointment dates should display correctly (not 'Null天前')" -ForegroundColor White
Write-Host "- Complete patient health profile information available" -ForegroundColor White
