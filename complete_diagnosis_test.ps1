# Complete Diagnosis Test with Appointment Creation

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Complete Diagnosis Function Test ===" -ForegroundColor Green

# Step 1: Patient login and create appointment
Write-Host "`n1. Patient Login and Create Appointment" -ForegroundColor Yellow

$patientLoginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $patientLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $patientLoginData -Headers $headers
    Write-Host "Patient Login Success" -ForegroundColor Green
    
    $patientToken = $patientLoginResponse.data.token
    $patientAuthHeaders = $headers.Clone()
    $patientAuthHeaders["Authorization"] = "Bearer $patientToken"
    
    # Get patient health profiles
    try {
        $profilesResponse = Invoke-RestMethod -Uri "$baseUrl/api/health/profiles" -Method GET -Headers $patientAuthHeaders
        
        if ($profilesResponse.data.content -and $profilesResponse.data.content.Count -gt 0) {
            $profile = $profilesResponse.data.content[0]
            Write-Host "Found health profile: $($profile.ownerName)" -ForegroundColor Cyan
            
            # Get available schedules
            $today = Get-Date -Format "yyyy-MM-dd"
            $schedulesUrl = "$baseUrl/api/appointments/schedules/available?date=$today"
            
            try {
                $schedulesResponse = Invoke-RestMethod -Uri $schedulesUrl -Method GET -Headers $patientAuthHeaders
                
                if ($schedulesResponse.data -and $schedulesResponse.data.Count -gt 0) {
                    $schedule = $schedulesResponse.data[0]
                    Write-Host "Found available schedule: Dr. $($schedule.doctorName)" -ForegroundColor Cyan
                    
                    # Create appointment
                    $appointmentData = @{
                        scheduleId = $schedule.id
                        profileId = $profile.id
                        notes = "Test appointment for diagnosis feature"
                    } | ConvertTo-Json
                    
                    $appointmentResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments" -Method POST -Body $appointmentData -Headers $patientAuthHeaders
                    
                    if ($appointmentResponse.data) {
                        Write-Host "Appointment created successfully: ID $($appointmentResponse.data.id)" -ForegroundColor Green
                        $appointmentId = $appointmentResponse.data.id
                        $doctorId = $schedule.doctorId
                        
                        # Step 2: Doctor login and test diagnosis
                        Write-Host "`n2. Doctor Login and Diagnosis Test" -ForegroundColor Yellow
                        
                        $doctorLoginData = @{
                            phoneNumber = "***********"
                            password = "123456"
                        } | ConvertTo-Json
                        
                        $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
                        Write-Host "Doctor Login Success" -ForegroundColor Green
                        
                        $doctorToken = $doctorLoginResponse.data.token
                        $doctorAuthHeaders = $headers.Clone()
                        $doctorAuthHeaders["Authorization"] = "Bearer $doctorToken"
                        
                        # Get doctor appointments
                        $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
                        $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $doctorAuthHeaders
                        
                        Write-Host "Doctor Appointments Retrieved: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
                        
                        if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                            $appointment = $appointmentsResponse.data.content | Where-Object { $_.id -eq $appointmentId }
                            
                            if ($appointment) {
                                Write-Host "`nAppointment Details:" -ForegroundColor White
                                Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
                                Write-Host "  Patient: $($appointment.profileOwnerName)" -ForegroundColor Yellow
                                Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
                                Write-Host "  Has Diagnosis: $($appointment.hasDiagnosis)" -ForegroundColor Magenta
                                
                                # Step 3: Create diagnosis prescription
                                Write-Host "`n3. Create Diagnosis Prescription" -ForegroundColor Yellow
                                
                                $prescriptionData = @{
                                    profileId = $appointment.profileId
                                    appointmentId = $appointment.id
                                    diagnosis = "Common cold with mild symptoms - test diagnosis"
                                    medications = @(
                                        @{
                                            name = "Paracetamol"
                                            specification = "500mg"
                                            quantity = 10
                                            frequency = "3 times daily"
                                            dosage = "1 tablet after meals"
                                            notes = "Take with water"
                                        },
                                        @{
                                            name = "Vitamin C"
                                            specification = "100mg"
                                            quantity = 30
                                            frequency = "1 time daily"
                                            dosage = "1 tablet in morning"
                                            notes = "Boost immunity"
                                        }
                                    )
                                } | ConvertTo-Json -Depth 3
                                
                                try {
                                    $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescribe"
                                    $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $doctorAuthHeaders
                                    
                                    Write-Host "Prescription Created Successfully!" -ForegroundColor Green
                                    Write-Host "Prescription ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
                                    Write-Host "Diagnosis: $($prescriptionResponse.data.prescription.diagnosis)" -ForegroundColor Yellow
                                    Write-Host "Medications Count: $($prescriptionResponse.data.prescription.medications.Count)" -ForegroundColor Yellow
                                    
                                    # Step 4: Verify appointment status update
                                    Write-Host "`n4. Verify Status Update" -ForegroundColor Yellow
                                    
                                    Start-Sleep -Seconds 2
                                    $updatedResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $doctorAuthHeaders
                                    $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $appointment.id }
                                    
                                    if ($updatedAppointment) {
                                        Write-Host "Updated Status:" -ForegroundColor Green
                                        Write-Host "  Appointment Status: $($updatedAppointment.status)" -ForegroundColor Gray
                                        Write-Host "  Has Diagnosis: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                                        
                                        if ($updatedAppointment.hasDiagnosis -eq $true) {
                                            Write-Host "`n✅ SUCCESS: Diagnosis function is working perfectly!" -ForegroundColor Green
                                            Write-Host "   - Diagnosis prescription created successfully" -ForegroundColor White
                                            Write-Host "   - Appointment status updated automatically" -ForegroundColor White
                                            Write-Host "   - Diagnosis status correctly displayed" -ForegroundColor White
                                        } else {
                                            Write-Host "`n⚠️  WARNING: Diagnosis status not updated" -ForegroundColor Yellow
                                        }
                                    }
                                    
                                    # Step 5: Test prescription retrieval
                                    Write-Host "`n5. Test Prescription Retrieval" -ForegroundColor Yellow
                                    
                                    try {
                                        $prescriptionDetailUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescription"
                                        $prescriptionDetailResponse = Invoke-RestMethod -Uri $prescriptionDetailUrl -Method GET -Headers $doctorAuthHeaders
                                        
                                        Write-Host "Prescription Details Retrieved:" -ForegroundColor Green
                                        Write-Host "  Diagnosis: $($prescriptionDetailResponse.data.diagnosis)" -ForegroundColor Yellow
                                        Write-Host "  Medications:" -ForegroundColor Yellow
                                        
                                        foreach ($med in $prescriptionDetailResponse.data.medications) {
                                            Write-Host "    - $($med.name) ($($med.specification)) x$($med.quantity)" -ForegroundColor Cyan
                                            Write-Host "      $($med.frequency), $($med.dosage)" -ForegroundColor Gray
                                        }
                                        
                                    } catch {
                                        Write-Host "Failed to retrieve prescription details: $($_.Exception.Message)" -ForegroundColor Red
                                    }
                                    
                                } catch {
                                    Write-Host "Prescription Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
                                    if ($_.ErrorDetails.Message) {
                                        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
                                    }
                                }
                                
                            } else {
                                Write-Host "Created appointment not found in doctor's list" -ForegroundColor Red
                            }
                        } else {
                            Write-Host "No appointments found for doctor" -ForegroundColor Yellow
                        }
                        
                    } else {
                        Write-Host "Failed to create appointment" -ForegroundColor Red
                    }
                    
                } else {
                    Write-Host "No available schedules found" -ForegroundColor Yellow
                }
                
            } catch {
                Write-Host "Failed to get schedules: $($_.Exception.Message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "No health profiles found" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "Failed to get health profiles: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Patient login failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green

Write-Host "`nFrontend Access:" -ForegroundColor Cyan
Write-Host "- Frontend: http://localhost:5174" -ForegroundColor White
Write-Host "- Doctor Appointments: http://localhost:5174/doctor/appointments" -ForegroundColor White
Write-Host "- Doctor Account: *********** / 123456" -ForegroundColor White

Write-Host "`nDiagnosis Features:" -ForegroundColor Yellow
Write-Host "1. Click diagnosis button (🩺) in appointment list" -ForegroundColor White
Write-Host "2. Fill diagnosis information and prescription details" -ForegroundColor White
Write-Host "3. Submit to create prescription and update appointment" -ForegroundColor White
Write-Host "4. View diagnosis status and prescription history" -ForegroundColor White
