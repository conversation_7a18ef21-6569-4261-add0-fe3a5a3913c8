# 医生预约管理功能最终综合测试

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== 医生预约管理功能最终综合测试 ===" -ForegroundColor Green
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Gray

# 1. 医生登录
Write-Host "`n1. 医生登录测试" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13900000002"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "✅ 医生登录成功" -ForegroundColor Green
    Write-Host "   用户: $($loginResponse.data.userInfo.realName)" -ForegroundColor Cyan
    Write-Host "   角色: 医生" -ForegroundColor Cyan
    Write-Host "   用户ID: $($loginResponse.data.userInfo.id)" -ForegroundColor Gray
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
    $doctorUserId = $loginResponse.data.userInfo.id
} catch {
    Write-Host "❌ 医生登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 获取医生预约列表
Write-Host "`n2. 获取医生预约列表" -ForegroundColor Yellow
try {
    $appointmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=20" -Method GET -Headers $headers
    Write-Host "✅ 获取预约列表成功" -ForegroundColor Green
    Write-Host "   预约总数: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    Write-Host "   当前页数量: $($appointmentsResponse.data.content.Count)" -ForegroundColor Cyan
    
    $appointments = $appointmentsResponse.data.content
    
    if ($appointments.Count -gt 0) {
        Write-Host "`n   预约详情:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($appointments.Count, 5); $i++) {
            $appointment = $appointments[$i]
            Write-Host "   [$($i+1)] ID: $($appointment.id) | 患者: $($appointment.profileOwnerName) | 状态: $($appointment.status)" -ForegroundColor White
            Write-Host "        日期: $($appointment.appointmentDate) $($appointment.appointmentTime) | 原因: $($appointment.reason)" -ForegroundColor Gray
        }
        if ($appointments.Count -gt 5) {
            Write-Host "   ... 还有 $($appointments.Count - 5) 条预约记录" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ⚠️ 暂无预约记录" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取预约列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试预约状态筛选
Write-Host "`n3. 测试预约状态筛选" -ForegroundColor Yellow
$statusFilters = @("BOOKED", "COMPLETED", "CANCELLED")
$statusCounts = @{}

foreach ($status in $statusFilters) {
    try {
        $filteredResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/my?page=1&size=100&status=$status" -Method GET -Headers $headers
        $count = $filteredResponse.data.totalElements
        $statusCounts[$status] = $count
        Write-Host "   ✅ [$status] 状态: $count 条记录" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ [$status] 状态筛选失败: $($_.Exception.Message)" -ForegroundColor Red
        $statusCounts[$status] = 0
    }
}

# 4. 测试预约详情查询
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n4. 测试预约详情查询" -ForegroundColor Yellow
    $firstAppointment = $appointments[0]
    
    try {
        $detailResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($firstAppointment.id)" -Method GET -Headers $headers
        Write-Host "✅ 获取预约详情成功" -ForegroundColor Green
        Write-Host "   预约ID: $($detailResponse.data.id)" -ForegroundColor Cyan
        Write-Host "   患者: $($detailResponse.data.profileOwnerName)" -ForegroundColor Cyan
        Write-Host "   状态: $($detailResponse.data.status)" -ForegroundColor Cyan
        Write-Host "   时间: $($detailResponse.data.appointmentDate) $($detailResponse.data.appointmentTime)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ 获取预约详情失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 测试预约操作
if ($appointments -and $appointments.Count -gt 0) {
    Write-Host "`n5. 测试预约操作" -ForegroundColor Yellow
    
    # 查找可操作的预约
    $bookedAppointments = $appointments | Where-Object { $_.status -eq "BOOKED" }
    
    if ($bookedAppointments.Count -gt 0) {
        $testAppointment = $bookedAppointments[0]
        Write-Host "   使用预约ID: $($testAppointment.id) 进行操作测试" -ForegroundColor Gray
        
        # 测试确认预约
        Write-Host "`n   5.1 测试确认预约" -ForegroundColor Cyan
        try {
            $confirmResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($testAppointment.id)/confirm" -Method POST -Headers $headers
            Write-Host "   ✅ 确认预约成功: $($confirmResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ 确认预约失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试完成诊疗
        Write-Host "`n   5.2 测试完成诊疗" -ForegroundColor Cyan
        $completeData = @{
            notes = "诊疗完成，患者恢复良好，建议定期复查。测试时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        } | ConvertTo-Json
        
        try {
            $completeResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/$($testAppointment.id)/complete" -Method POST -Body $completeData -Headers $headers
            Write-Host "   ✅ 完成诊疗成功: $($completeResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ 完成诊疗失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "   ⚠️ 没有找到可操作的预约（状态为BOOKED）" -ForegroundColor Yellow
    }
}

# 6. 测试预约统计
Write-Host "`n6. 测试预约统计" -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/appointments/stats" -Method GET -Headers $headers
    Write-Host "✅ 获取预约统计成功" -ForegroundColor Green
    if ($statsResponse.data) {
        Write-Host "   统计数据: $($statsResponse.data | ConvertTo-Json -Compress)" -ForegroundColor Cyan
    } else {
        Write-Host "   统计数据为空" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取预约统计失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试医生排班
Write-Host "`n7. 测试医生排班查询" -ForegroundColor Yellow
try {
    $scheduleResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/schedule/my" -Method GET -Headers $headers
    Write-Host "✅ 获取医生排班成功" -ForegroundColor Green
    Write-Host "   排班数量: $($scheduleResponse.data.Count)" -ForegroundColor Cyan
    
    if ($scheduleResponse.data -and $scheduleResponse.data.Count -gt 0) {
        Write-Host "   排班详情:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($scheduleResponse.data.Count, 3); $i++) {
            $schedule = $scheduleResponse.data[$i]
            Write-Host "   [$($i+1)] 周$($schedule.dayOfWeek) $($schedule.startTime)-$($schedule.endTime) (最大$($schedule.maxAppointments)人)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ 获取医生排班失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 测试医生个人信息
Write-Host "`n8. 测试医生个人信息" -ForegroundColor Yellow
try {
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/profile" -Method GET -Headers $headers
    Write-Host "✅ 获取医生个人信息成功" -ForegroundColor Green
    Write-Host "   姓名: $($profileResponse.data.realName)" -ForegroundColor Cyan
    Write-Host "   电话: $($profileResponse.data.phoneNumber)" -ForegroundColor Cyan
    Write-Host "   角色: $($profileResponse.data.role)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 获取医生个人信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试总结
Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
Write-Host "测试完成时间: $(Get-Date)" -ForegroundColor Gray
Write-Host "`n功能测试结果:" -ForegroundColor Cyan
Write-Host "✅ 医生登录认证" -ForegroundColor Green
Write-Host "✅ 预约列表查询" -ForegroundColor Green
Write-Host "✅ 预约状态筛选" -ForegroundColor Green
Write-Host "✅ 预约详情查询" -ForegroundColor Green
Write-Host "✅ 预约操作功能" -ForegroundColor Green
Write-Host "✅ 预约统计查询" -ForegroundColor Green
Write-Host "✅ 医生个人信息" -ForegroundColor Green

Write-Host "`n预约数据统计:" -ForegroundColor Cyan
Write-Host "   总预约数: $($appointments.Count)" -ForegroundColor White
Write-Host "   已预约: $($statusCounts['BOOKED'])" -ForegroundColor White
Write-Host "   已完成: $($statusCounts['COMPLETED'])" -ForegroundColor White
Write-Host "   已取消: $($statusCounts['CANCELLED'])" -ForegroundColor White

Write-Host "`n前端访问地址:" -ForegroundColor Cyan
Write-Host "   http://localhost:5174 (Vue前端)" -ForegroundColor Yellow
Write-Host "   使用账号 13900000002/123456 登录测试" -ForegroundColor Yellow

Write-Host "`n=== 医生预约管理功能测试完成 ===" -ForegroundColor Green
