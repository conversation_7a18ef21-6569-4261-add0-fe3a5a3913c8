# Check Doctor Data and Permissions

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Check Doctor Data and Permissions ===" -ForegroundColor Green

# Test different doctor accounts
$doctorAccounts = @(
    @{ phone = "***********"; name = "王健康" },
    @{ phone = "***********"; name = "Test Doctor" },
    @{ phone = "***********"; name = "李医生" },
    @{ phone = "***********"; name = "张医生" }
)

foreach ($account in $doctorAccounts) {
    Write-Host "`nTesting Doctor Account: $($account.phone) ($($account.name))" -ForegroundColor Yellow
    
    # 1. Login
    $loginData = @{
        phoneNumber = $account.phone
        password = "123456"
    } | ConvertTo-Json

    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        Write-Host "  ✅ Login Success" -ForegroundColor Green
        Write-Host "  User ID: $($loginResponse.data.user.id)" -ForegroundColor Gray
        Write-Host "  Role: $($loginResponse.data.user.role)" -ForegroundColor Gray
        Write-Host "  Name: $($loginResponse.data.user.realName)" -ForegroundColor Gray
        
        $token = $loginResponse.data.token
        $authHeaders = $headers.Clone()
        $authHeaders["Authorization"] = "Bearer $token"
        
        # 2. Test Doctor Appointments API
        try {
            $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=5"
            $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
            Write-Host "  ✅ Appointments API Success" -ForegroundColor Green
            Write-Host "  Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Gray
            
            if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                $firstAppointment = $appointmentsResponse.data.content[0]
                Write-Host "  Sample Appointment:" -ForegroundColor Cyan
                Write-Host "    ID: $($firstAppointment.id)" -ForegroundColor Gray
                Write-Host "    Patient: $($firstAppointment.profileOwnerName)" -ForegroundColor Gray
                Write-Host "    Status: $($firstAppointment.status)" -ForegroundColor Gray
                Write-Host "    Date: $($firstAppointment.appointmentDate)" -ForegroundColor Gray
                Write-Host "    Time: $($firstAppointment.appointmentTime)" -ForegroundColor Gray
                
                # Check if patient info is complete
                if ($firstAppointment.profileOwnerName -and $firstAppointment.profileOwnerName -ne "null") {
                    Write-Host "    ✅ Patient name loaded correctly" -ForegroundColor Green
                } else {
                    Write-Host "    ❌ Patient name missing or null" -ForegroundColor Red
                }
                
                if ($firstAppointment.profileGender) {
                    Write-Host "    ✅ Patient gender: $($firstAppointment.profileGender)" -ForegroundColor Green
                } else {
                    Write-Host "    ❌ Patient gender missing" -ForegroundColor Red
                }
                
                if ($firstAppointment.profileAge) {
                    Write-Host "    ✅ Patient age: $($firstAppointment.profileAge)" -ForegroundColor Green
                } else {
                    Write-Host "    ❌ Patient age missing" -ForegroundColor Red
                }
                
                if ($firstAppointment.profilePhoneNumber) {
                    Write-Host "    ✅ Patient phone: $($firstAppointment.profilePhoneNumber)" -ForegroundColor Green
                } else {
                    Write-Host "    ❌ Patient phone missing" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "  ❌ Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                Write-Host "  Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
        
        # 3. Test User Profile API
        try {
            $profileUrl = "$baseUrl/api/user/profile"
            $profileResponse = Invoke-RestMethod -Uri $profileUrl -Method GET -Headers $authHeaders
            Write-Host "  ✅ Profile API Success" -ForegroundColor Green
            Write-Host "  Profile Role: $($profileResponse.data.role)" -ForegroundColor Gray
        } catch {
            Write-Host "  ❌ Profile API Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

# Check if there are any appointments in the database
Write-Host "`nChecking Database for Appointments..." -ForegroundColor Yellow

# Try to login as a regular user to create an appointment first
Write-Host "`nTrying to login as patient to check appointments..." -ForegroundColor Cyan
$patientLoginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $patientLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $patientLoginData -Headers $headers
    Write-Host "Patient Login Success: $($patientLoginResponse.data.user.realName)" -ForegroundColor Green
    
    $patientToken = $patientLoginResponse.data.token
    $patientHeaders = $headers.Clone()
    $patientHeaders["Authorization"] = "Bearer $patientToken"
    
    # Check patient's appointments
    try {
        $patientAppointmentsUrl = "$baseUrl/api/appointments/my?page=1&size=10"
        $patientAppointmentsResponse = Invoke-RestMethod -Uri $patientAppointmentsUrl -Method GET -Headers $patientHeaders
        Write-Host "Patient has $($patientAppointmentsResponse.data.totalElements) appointments" -ForegroundColor Green
        
        if ($patientAppointmentsResponse.data.content -and $patientAppointmentsResponse.data.content.Count -gt 0) {
            $appointment = $patientAppointmentsResponse.data.content[0]
            Write-Host "Sample Patient Appointment:" -ForegroundColor Cyan
            Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
            Write-Host "  Doctor: $($appointment.doctorName)" -ForegroundColor Gray
            Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
            Write-Host "  Date: $($appointment.appointmentDate)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Patient appointments check failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Patient login failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Doctor Data Check Complete ===" -ForegroundColor Green
