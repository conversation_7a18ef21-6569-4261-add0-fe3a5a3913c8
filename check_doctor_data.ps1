# 检查医生和科室数据

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== 检查医生和科室数据 ===" -ForegroundColor Green

# 1. 用户登录获取token
Write-Host "`n1. 用户登录" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "✅ 用户登录成功" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 获取所有科室
Write-Host "`n2. 获取科室列表" -ForegroundColor Yellow
try {
    $departmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments" -Method GET -Headers $headers
    Write-Host "✅ 获取科室成功" -ForegroundColor Green
    Write-Host "科室数量: $($departmentsResponse.data.Count)" -ForegroundColor Cyan
    
    $departments = $departmentsResponse.data
    
    if ($departments.Count -gt 0) {
        Write-Host "`n科室列表:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $departments.Count; $i++) {
            $dept = $departments[$i]
            Write-Host "  [$($i+1)] ID: $($dept.id) | 名称: $($dept.name) | 描述: $($dept.description)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ 获取科室失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 检查每个科室的医生
Write-Host "`n3. 检查各科室医生情况" -ForegroundColor Yellow
foreach ($dept in $departments) {
    Write-Host "`n科室: $($dept.name) (ID: $($dept.id))" -ForegroundColor Cyan
    
    try {
        $doctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/$($dept.id)/doctors?page=1&size=20" -Method GET -Headers $headers
        $doctorCount = $doctorsResponse.data.totalElements
        $doctors = $doctorsResponse.data.content
        
        Write-Host "  医生数量: $doctorCount" -ForegroundColor Green
        
        if ($doctors.Count -gt 0) {
            Write-Host "  医生列表:" -ForegroundColor White
            for ($j = 0; $j -lt [Math]::Min($doctors.Count, 3); $j++) {
                $doctor = $doctors[$j]
                Write-Host "    - $($doctor.realName) | 职称: $($doctor.title) | 状态: $($doctor.status)" -ForegroundColor Gray
            }
            if ($doctors.Count -gt 3) {
                Write-Host "    ... 还有 $($doctors.Count - 3) 位医生" -ForegroundColor Gray
            }
        } else {
            Write-Host "  ⚠️ 该科室暂无医生" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ❌ 获取科室医生失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 搜索所有医生
Write-Host "`n4. 搜索所有医生" -ForegroundColor Yellow
try {
    $allDoctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/doctors?page=1&size=50" -Method GET -Headers $headers
    Write-Host "✅ 获取所有医生成功" -ForegroundColor Green
    Write-Host "医生总数: $($allDoctorsResponse.data.totalElements)" -ForegroundColor Cyan
    
    $allDoctors = $allDoctorsResponse.data.content
    
    if ($allDoctors.Count -gt 0) {
        Write-Host "`n所有医生列表:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($allDoctors.Count, 10); $i++) {
            $doctor = $allDoctors[$i]
            Write-Host "  [$($i+1)] $($doctor.realName) | 科室ID: $($doctor.departmentId) | 职称: $($doctor.title) | 状态: $($doctor.status)" -ForegroundColor White
        }
        if ($allDoctors.Count -gt 10) {
            Write-Host "  ... 还有 $($allDoctors.Count - 10) 位医生" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ 获取所有医生失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试特定科室（内科）的医生查询
Write-Host "`n5. 测试内科医生查询" -ForegroundColor Yellow
$internalMedicine = $departments | Where-Object { $_.name -eq "内科" } | Select-Object -First 1

if ($internalMedicine) {
    Write-Host "内科ID: $($internalMedicine.id)" -ForegroundColor Cyan
    
    try {
        $internalDoctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/$($internalMedicine.id)/doctors?page=1&size=20" -Method GET -Headers $headers
        Write-Host "✅ 内科医生查询成功" -ForegroundColor Green
        Write-Host "内科医生数量: $($internalDoctorsResponse.data.totalElements)" -ForegroundColor Cyan
        
        $internalDoctors = $internalDoctorsResponse.data.content
        if ($internalDoctors.Count -gt 0) {
            Write-Host "内科医生详情:" -ForegroundColor White
            foreach ($doctor in $internalDoctors) {
                Write-Host "  - $($doctor.realName) | 用户ID: $($doctor.userId) | 职称: $($doctor.title)" -ForegroundColor Gray
                Write-Host "    专长: $($doctor.specialty)" -ForegroundColor Gray
                Write-Host "    状态: $($doctor.status)" -ForegroundColor Gray
                Write-Host ""
            }
        }
    } catch {
        Write-Host "❌ 内科医生查询失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ 未找到内科" -ForegroundColor Yellow
}

Write-Host "`n=== 数据检查完成 ===" -ForegroundColor Green
