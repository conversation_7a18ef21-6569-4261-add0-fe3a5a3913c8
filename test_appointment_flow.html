<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover { background: #357abd; }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active { background: #4A90E2; color: white; }
        .step.completed { background: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>预约流程步骤修复验证</h1>
    
    <div class="test-card">
        <h2>问题描述</h2>
        <div class="status error">
            <strong>修复前问题：</strong>用户在第4步选择时间段后，"下一步"按钮无法点击，无法进入第5步确认预约。
        </div>
        <div class="status success">
            <strong>修复方案：</strong>将 nextStep() 函数中的条件从 <code>currentStep < 4</code> 改为 <code>currentStep < 5</code>
        </div>
    </div>

    <div class="test-card">
        <h2>预约流程步骤模拟</h2>
        <div class="step-indicator">
            <div class="step completed">1</div>
            <div class="step completed">2</div>
            <div class="step completed">3</div>
            <div class="step active" id="step4">4</div>
            <div class="step" id="step5">5</div>
        </div>
        
        <div id="stepContent">
            <h3>第4步：选择预约时间</h3>
            <div class="status info">
                <strong>当前状态：</strong>
                <ul>
                    <li>✅ 已选择健康档案：张三</li>
                    <li>✅ 已选择科室：内科</li>
                    <li>✅ 已选择医生：王健康主任医师</li>
                    <li>✅ 已选择日期：2025年6月17日（周二）</li>
                    <li id="scheduleStatus">⏳ 等待选择时间段...</li>
                </ul>
            </div>
            
            <h4>可预约时间段：</h4>
            <button class="button" onclick="selectSchedule(1)" id="schedule1">
                王健康主任医师 08:00-12:00 (剩余15个号源)
            </button>
            <button class="button" onclick="selectSchedule(2)" id="schedule2" style="background: #4A90E2;">
                王健康主任医师 14:00-18:00 (剩余16个号源) ✓
            </button>
            
            <div style="margin-top: 20px;">
                <button class="button" onclick="prevStep()">上一步</button>
                <button class="button" id="nextBtn" onclick="nextStep()">下一步</button>
            </div>
        </div>
    </div>

    <div class="test-card">
        <h2>修复验证</h2>
        <div id="testResult" class="status info">
            <strong>测试状态：</strong>等待用户操作...
        </div>
        
        <h3>测试步骤：</h3>
        <ol>
            <li>上方已模拟选择了下午时间段（蓝色高亮）</li>
            <li>点击"下一步"按钮测试是否能进入第5步</li>
            <li>验证步骤指示器是否正确更新</li>
        </ol>
        
        <h3>预期结果：</h3>
        <ul>
            <li>✅ "下一步"按钮应该可以点击</li>
            <li>✅ 点击后应该进入第5步确认页面</li>
            <li>✅ 步骤指示器应该显示第5步为活跃状态</li>
        </ul>
    </div>

    <div class="test-card">
        <h2>实际前端测试</h2>
        <div class="status warning">
            <strong>请在实际前端进行测试：</strong>
            <ol>
                <li>打开 <a href="http://localhost:5174/appointments" target="_blank">http://localhost:5174/appointments</a></li>
                <li>使用账号 13800000011/123456 登录</li>
                <li>完成预约流程到第4步</li>
                <li>选择时间段后验证"下一步"按钮是否可用</li>
            </ol>
        </div>
    </div>

    <script>
        let currentStep = 4;
        let selectedSchedule = 2; // 模拟已选择下午时段

        function selectSchedule(scheduleId) {
            // 重置所有按钮样式
            document.getElementById('schedule1').style.background = '#4A90E2';
            document.getElementById('schedule2').style.background = '#4A90E2';
            document.getElementById('schedule1').innerHTML = '王健康主任医师 08:00-12:00 (剩余15个号源)';
            document.getElementById('schedule2').innerHTML = '王健康主任医师 14:00-18:00 (剩余16个号源)';
            
            // 设置选中样式
            const selectedBtn = document.getElementById('schedule' + scheduleId);
            selectedBtn.style.background = '#357abd';
            selectedBtn.innerHTML += ' ✓';
            
            selectedSchedule = scheduleId;
            document.getElementById('scheduleStatus').innerHTML = '✅ 已选择时间段：' + 
                (scheduleId === 1 ? '上午 08:00-12:00' : '下午 14:00-18:00');
            
            // 启用下一步按钮
            document.getElementById('nextBtn').disabled = false;
            document.getElementById('nextBtn').style.background = '#4A90E2';
            
            updateTestResult('success', '时间段选择成功！"下一步"按钮已启用。');
        }

        function nextStep() {
            if (currentStep < 5) {  // 修复后的逻辑
                currentStep++;
                
                // 更新步骤指示器
                document.getElementById('step4').className = 'step completed';
                document.getElementById('step5').className = 'step active';
                
                // 更新内容
                document.getElementById('stepContent').innerHTML = `
                    <h3>第5步：确认预约信息</h3>
                    <div class="status success">
                        <strong>✅ 成功进入第5步！</strong>
                        <p>修复生效：nextStep() 函数现在允许从第4步进入第5步。</p>
                    </div>
                    <div class="status info">
                        <strong>预约信息确认：</strong>
                        <ul>
                            <li>就诊人：张三</li>
                            <li>科室：内科</li>
                            <li>医生：王健康主任医师</li>
                            <li>时间：2025年6月17日 ${selectedSchedule === 1 ? '08:00-12:00' : '14:00-18:00'}</li>
                        </ul>
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="button" onclick="prevStep()">上一步</button>
                        <button class="button" onclick="confirmBooking()">确认预约</button>
                    </div>
                `;
                
                updateTestResult('success', '✅ 修复成功！已成功从第4步进入第5步确认页面。');
            } else {
                updateTestResult('error', '❌ 无法继续：已到达最后一步。');
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                updateTestResult('info', '返回上一步：第' + currentStep + '步');
            }
        }

        function confirmBooking() {
            updateTestResult('success', '🎉 预约确认成功！整个流程已完成。');
        }

        function updateTestResult(type, message) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = 'status ' + type;
            resultDiv.innerHTML = '<strong>测试结果：</strong>' + message;
        }

        // 初始化
        window.onload = function() {
            // 模拟已选择下午时段
            selectSchedule(2);
        };
    </script>
</body>
</html>
