<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #357abd;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <h1>前端API调试工具</h1>
    
    <div class="section">
        <h2>1. 用户登录</h2>
        <button class="button" onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. 获取科室列表</h2>
        <button class="button" onclick="testDepartments()">获取科室</button>
        <div id="departmentsResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. 获取科室医生</h2>
        <button class="button" onclick="testDepartmentDoctors(1)">内科医生 (ID: 1)</button>
        <button class="button" onclick="testDepartmentDoctors(2)">外科医生 (ID: 2)</button>
        <button class="button" onclick="testDepartmentDoctors(3)">儿科医生 (ID: 3)</button>
        <div id="doctorsResult" class="result"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8080';
        let authToken = '';

        async function apiCall(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                return { success: true, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';

            const loginData = {
                phoneNumber: '13800000011',
                password: '123456'
            };

            const result = await apiCall(`${baseUrl}/api/user/login`, {
                method: 'POST',
                body: JSON.stringify(loginData)
            });

            if (result.success && result.data.code === 200) {
                authToken = result.data.data.token;
                resultDiv.className = 'result success';
                resultDiv.textContent = `登录成功！\n用户: ${result.data.data.userInfo.realName}\nToken: ${authToken}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录失败: ${JSON.stringify(result, null, 2)}`;
            }
        }

        async function testDepartments() {
            const resultDiv = document.getElementById('departmentsResult');
            resultDiv.textContent = '正在获取科室列表...';

            const result = await apiCall(`${baseUrl}/api/appointments/departments`);

            if (result.success && result.data.code === 200) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `科室列表获取成功！\n科室数量: ${result.data.data.length}\n\n科室详情:\n${JSON.stringify(result.data.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取科室失败: ${JSON.stringify(result, null, 2)}`;
            }
        }

        async function testDepartmentDoctors(departmentId) {
            const resultDiv = document.getElementById('doctorsResult');
            resultDiv.textContent = `正在获取科室 ${departmentId} 的医生列表...`;

            const result = await apiCall(`${baseUrl}/api/appointments/departments/${departmentId}/doctors?page=1&size=20`);

            if (result.success && result.data.code === 200) {
                const doctors = result.data.data.content || [];
                resultDiv.className = 'result success';
                resultDiv.textContent = `科室 ${departmentId} 医生获取成功！
医生数量: ${doctors.length}
总数: ${result.data.data.totalElements}

响应结构:
${JSON.stringify(result.data, null, 2)}

医生详情:
${doctors.map(doctor => `- ${doctor.realName} (${doctor.title})`).join('\n')}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取科室 ${departmentId} 医生失败: ${JSON.stringify(result, null, 2)}`;
            }
        }

        // 页面加载时自动登录
        window.onload = function() {
            testLogin();
        };
    </script>
</body>
</html>
