package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Diagnosis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 诊断记录Repository
 */
@Repository
public interface DiagnosisRepository extends JpaRepository<Diagnosis, Long> {
    
    /**
     * 根据预约ID查找诊断记录
     */
    Optional<Diagnosis> findByAppointmentId(Long appointmentId);
    
    /**
     * 查找医生的诊断记录
     */
    @Query(value = "SELECT d FROM Diagnosis d " +
           "JOIN FETCH d.appointment a " +
           "JOIN FETCH a.profile p " +
           "JOIN FETCH a.user u " +
           "WHERE d.doctorId = :doctorId " +
           "ORDER BY d.diagnosisTime DESC",
           countQuery = "SELECT COUNT(d) FROM Diagnosis d WHERE d.doctorId = :doctorId")
    Page<Diagnosis> findByDoctorId(@Param("doctorId") Long doctorId, Pageable pageable);
    
    /**
     * 查找患者的诊断记录
     */
    @Query(value = "SELECT d FROM Diagnosis d " +
           "JOIN FETCH d.appointment a " +
           "JOIN FETCH d.doctor doc " +
           "JOIN FETCH doc.department dept " +
           "WHERE a.userId = :userId " +
           "ORDER BY d.diagnosisTime DESC",
           countQuery = "SELECT COUNT(d) FROM Diagnosis d " +
           "JOIN d.appointment a WHERE a.userId = :userId")
    Page<Diagnosis> findByPatientId(@Param("userId") Long userId, Pageable pageable);
}
