package com.ruanjianjiaGou.ruanjianjiaGou.dto.diagnosis;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 诊断创建DTO
 */
@Data
public class DiagnosisCreateDTO {
    
    @NotNull(message = "预约ID不能为空")
    private Long appointmentId;
    
    @NotBlank(message = "诊断结果不能为空")
    private String diagnosis;
    
    private String treatment; // 治疗方案
    
    private String prescription; // 处方
    
    private String notes; // 备注
    
    private String followUpAdvice; // 随访建议
}
