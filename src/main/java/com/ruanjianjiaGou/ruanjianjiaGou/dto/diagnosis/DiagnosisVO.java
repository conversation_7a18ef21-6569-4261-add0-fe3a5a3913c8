package com.ruanjianjiaGou.ruanjianjiaGou.dto.diagnosis;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 诊断信息VO
 */
@Data
public class DiagnosisVO {
    
    private Long id;
    
    private Long appointmentId;
    
    private String diagnosis; // 诊断结果
    
    private String treatment; // 治疗方案
    
    private String prescription; // 处方
    
    private String notes; // 备注
    
    private String followUpAdvice; // 随访建议
    
    private LocalDateTime diagnosisTime; // 诊断时间
    
    // 医生信息
    private Long doctorId;
    private String doctorName;
    private String doctorTitle;
    
    // 患者信息
    private String patientName;
    private String patientGender;
    private Integer patientAge;
    private String patientPhone;
}
