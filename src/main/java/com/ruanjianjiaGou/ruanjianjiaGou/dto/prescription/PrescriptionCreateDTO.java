package com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 开具处方请求DTO
 */
@Data
public class PrescriptionCreateDTO {
    
    @NotNull(message = "健康档案ID不能为空")
    private Long profileId;

    private Long consultationId; // 关联的问诊ID（可选）

    private Long appointmentId; // 关联的预约ID（可选）
    
    @NotBlank(message = "临床诊断不能为空")
    private String diagnosis;
    
    @NotEmpty(message = "药品列表不能为空")
    @Valid
    private List<MedicationDTO> medications;
}
