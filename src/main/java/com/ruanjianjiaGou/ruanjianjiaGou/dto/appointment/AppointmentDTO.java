package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;

/**
 * 预约记录DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentDTO {
    
    private Long id;
    private Long userId;
    private String status;
    private String statusDescription;
    private LocalDateTime createdAt;
    private String notes;
    
    // 排班信息
    private Long scheduleId;
    private LocalDate appointmentDate;
    private LocalTime appointmentTime;
    private LocalTime endTime;
    
    // 医生信息
    private Long doctorId;
    private String doctorName;
    private String doctorTitle;
    private String departmentName;
    
    // 就诊人信息
    private Long profileId;
    private String profileOwnerName;
    private String profileGender;
    private LocalDate profileBirthDate;
    private Integer profileAge;
    private String profileIdCardNumber;
    private String profileAvatarUrl;
    private String profilePhoneNumber;
    
    // 操作权限
    private Boolean canCancel;
    private Boolean canComplete;
}
