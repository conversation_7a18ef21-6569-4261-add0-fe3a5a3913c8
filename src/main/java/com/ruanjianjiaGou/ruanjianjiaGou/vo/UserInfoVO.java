package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import lombok.Data;
import java.time.LocalDate;

@Data
public class UserInfoVO {
    private Long id;
    private String nickname;
    private String phoneNumber;
    private String realName;
    private String gender;
    private LocalDate birthDate;
    private Integer age;
    private String idCardNumber;
    private String avatarUrl;

    // 角色相关字段
    private String role; // 用户角色：RESIDENT, DOCTOR, ADMIN
    private Boolean isDoctor;
    private Boolean isAdmin;
    private String doctorStatus; // 医生审核状态
    private String departmentName; // 科室名称
    private String title; // 职称

    public UserInfoVO(Long id, String nickname, String phoneNumber) {
        this.id = id;
        this.nickname = nickname;
        this.phoneNumber = phoneNumber;
        this.isDoctor = false;
        this.isAdmin = false;
    }

    public UserInfoVO() {}
}
