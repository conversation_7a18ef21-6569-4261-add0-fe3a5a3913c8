package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 诊断记录实体类
 */
@Data
@Entity
@Table(name = "diagnosis_records")
public class Diagnosis {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "appointment_id", nullable = false)
    private Long appointmentId;
    
    @OneToOne
    @JoinColumn(name = "appointment_id", insertable = false, updatable = false)
    private Appointment appointment;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @ManyToOne
    @JoinColumn(name = "doctor_id", insertable = false, updatable = false)
    private Doctor doctor;
    
    @Column(name = "diagnosis", nullable = false, columnDefinition = "TEXT")
    private String diagnosis; // 诊断结果
    
    @Column(name = "treatment", columnDefinition = "TEXT")
    private String treatment; // 治疗方案
    
    @Column(name = "prescription", columnDefinition = "TEXT")
    private String prescription; // 处方
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes; // 备注
    
    @Column(name = "follow_up_advice", columnDefinition = "TEXT")
    private String followUpAdvice; // 随访建议
    
    @CreationTimestamp
    @Column(name = "diagnosis_time", nullable = false)
    private LocalDateTime diagnosisTime; // 诊断时间
}
