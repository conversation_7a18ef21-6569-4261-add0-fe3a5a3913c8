package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.DoctorScheduleDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.AppointmentDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医生服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DoctorService {
    
    private final DoctorRepository doctorRepository;
    private final DoctorScheduleRepository scheduleRepository;
    private final AppointmentRepository appointmentRepository;
    private final HealthProfileRepository healthProfileRepository;
    private final HealthMetricRecordRepository healthRecordRepository;
    
    /**
     * 验证医生身份
     */
    private Doctor validateDoctor(Long userId) {
        log.info("验证医生身份: userId={}", userId);

        // 先查找医生记录
        Optional<Doctor> doctorOpt = doctorRepository.findByUserId(userId);
        if (!doctorOpt.isPresent()) {
            log.error("用户不是医生: userId={}", userId);
            throw new RuntimeException("您不是医生用户");
        }

        Doctor doctor = doctorOpt.get();
        log.info("找到医生记录: userId={}, status={}, realName={}", doctor.getUserId(), doctor.getStatus(), doctor.getRealName());

        // 检查医生状态
        if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
            log.error("医生状态不正确: userId={}, status={}", userId, doctor.getStatus());
            throw new RuntimeException("医生账户未通过审核，当前状态：" + doctor.getStatus().getDescription());
        }

        return doctor;
    }
    
    /**
     * 获取医生排班
     */
    @Transactional(readOnly = true)
    public List<DoctorScheduleDTO> getDoctorSchedules(Long userId, LocalDate startDate, LocalDate endDate) {
        Doctor doctor = validateDoctor(userId);
        
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = startDate.plusDays(30);
        }
        
        List<DoctorSchedule> schedules = scheduleRepository.findByDoctorIdAndScheduleDateBetweenOrderByScheduleDateAscStartTimeAsc(
                doctor.getUserId(), startDate, endDate);
        
        return schedules.stream()
                .map(this::convertToScheduleDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建排班
     */
    @Transactional
    public DoctorScheduleDTO createSchedule(Long userId, DoctorScheduleDTO scheduleDTO) {
        Doctor doctor = validateDoctor(userId);
        
        DoctorSchedule schedule = new DoctorSchedule();
        schedule.setDoctorId(doctor.getUserId());
        schedule.setScheduleDate(scheduleDTO.getScheduleDate());
        schedule.setStartTime(scheduleDTO.getStartTime());
        schedule.setEndTime(scheduleDTO.getEndTime());
        schedule.setTotalSlots(scheduleDTO.getTotalSlots());
        schedule.setBookedSlots(0);
        
        schedule = scheduleRepository.save(schedule);
        return convertToScheduleDTO(schedule);
    }
    
    /**
     * 更新排班
     */
    @Transactional
    public void updateSchedule(Long userId, Long scheduleId, DoctorScheduleDTO scheduleDTO) {
        Doctor doctor = validateDoctor(userId);
        
        DoctorSchedule schedule = scheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("排班不存在"));
        
        if (!schedule.getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限修改此排班");
        }
        
        schedule.setScheduleDate(scheduleDTO.getScheduleDate());
        schedule.setStartTime(scheduleDTO.getStartTime());
        schedule.setEndTime(scheduleDTO.getEndTime());
        schedule.setTotalSlots(scheduleDTO.getTotalSlots());
        
        scheduleRepository.save(schedule);
    }
    
    /**
     * 删除排班
     */
    @Transactional
    public void deleteSchedule(Long userId, Long scheduleId) {
        Doctor doctor = validateDoctor(userId);
        
        DoctorSchedule schedule = scheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("排班不存在"));
        
        if (!schedule.getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限删除此排班");
        }
        
        if (schedule.getBookedSlots() > 0) {
            throw new RuntimeException("已有预约的排班不能删除");
        }
        
        scheduleRepository.delete(schedule);
    }
    
    /**
     * 获取医生的预约
     */
    @Transactional(readOnly = true)
    public Page<AppointmentDTO> getDoctorAppointments(Long userId, int page, int size, String status) {
        Doctor doctor = validateDoctor(userId);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        
        Page<Appointment> appointments;
        if (status != null && !status.isEmpty()) {
            Appointment.AppointmentStatus appointmentStatus = Appointment.AppointmentStatus.valueOf(status.toUpperCase());
            appointments = appointmentRepository.findByDoctorIdAndStatus(doctor.getUserId(), appointmentStatus, pageable);
        } else {
            appointments = appointmentRepository.findByDoctorId(doctor.getUserId(), pageable);
        }
        
        return appointments.map(this::convertToAppointmentDTO);
    }
    
    /**
     * 确认预约
     */
    @Transactional
    public void confirmAppointment(Long userId, Long appointmentId) {
        Doctor doctor = validateDoctor(userId);
        
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));
        
        // 简化验证：通过schedule关联验证医生权限
        if (appointment.getSchedule() == null || !appointment.getSchedule().getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限操作此预约");
        }
        
        appointment.setStatus(Appointment.AppointmentStatus.BOOKED);
        appointmentRepository.save(appointment);
    }
    
    /**
     * 完成诊疗
     */
    @Transactional
    public void completeAppointment(Long userId, Long appointmentId, String notes) {
        Doctor doctor = validateDoctor(userId);
        
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));
        
        if (appointment.getSchedule() == null || !appointment.getSchedule().getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限操作此预约");
        }
        
        appointment.setStatus(Appointment.AppointmentStatus.COMPLETED);
        if (notes != null) {
            appointment.setNotes(notes);
        }
        appointmentRepository.save(appointment);
    }
    
    /**
     * 添加诊疗记录
     */
    @Transactional
    public void addTreatmentRecord(Long userId, Long appointmentId, Map<String, String> record) {
        Doctor doctor = validateDoctor(userId);
        
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));
        
        if (appointment.getSchedule() == null || !appointment.getSchedule().getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限操作此预约");
        }
        
        String diagnosis = record.get("diagnosis");
        String treatment = record.get("treatment");
        String prescription = record.get("prescription");
        
        String notes = String.format("诊断: %s\n治疗: %s\n处方: %s", 
                diagnosis != null ? diagnosis : "", 
                treatment != null ? treatment : "", 
                prescription != null ? prescription : "");
        
        appointment.setNotes(notes);
        appointmentRepository.save(appointment);
    }
    
    /**
     * 获取患者病历
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getPatientRecords(Long userId, Long patientId) {
        validateDoctor(userId);
        
        // 获取患者的预约记录
        List<Appointment> appointments = appointmentRepository.findByPatientIdOrderByAppointmentDateDesc(patientId);
        
        return appointments.stream()
                .map(appointment -> {
                    Map<String, Object> record = new HashMap<>();
                    record.put("appointmentId", appointment.getId());
                    record.put("createdAt", appointment.getCreatedAt());
                    record.put("notes", appointment.getNotes());
                    record.put("status", appointment.getStatus().name());
                    return record;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取医生统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getDoctorStatistics(Long userId) {
        Doctor doctor = validateDoctor(userId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总预约数
        long totalAppointments = appointmentRepository.countByDoctorId(doctor.getUserId());
        
        statistics.put("totalAppointments", totalAppointments);
        statistics.put("doctorInfo", convertToDoctorInfo(doctor));
        
        return statistics;
    }
    
    /**
     * 获取患者健康数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getPatientHealthData(Long userId, Long patientId) {
        validateDoctor(userId);
        
        Map<String, Object> healthData = new HashMap<>();
        
        // 获取患者的健康档案
        List<HealthProfile> profiles = healthProfileRepository.findByManagingUserId(patientId);
        
        if (!profiles.isEmpty()) {
            HealthProfile profile = profiles.get(0);
            
            // 获取健康记录（分页）
            Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "recordedAt"));
            Page<HealthMetricRecord> records = healthRecordRepository.findByProfileIdOrderByRecordedAtDesc(profile.getId(), pageable);
            
            healthData.put("profile", profile);
            healthData.put("recentRecords", records.getContent());
        }
        
        return healthData;
    }
    
    /**
     * 生成诊疗报告
     */
    @Transactional(readOnly = true)
    public Map<String, Object> generateTreatmentReport(Long userId, LocalDate startDate, LocalDate endDate) {
        Doctor doctor = validateDoctor(userId);
        
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        List<Appointment> appointments = appointmentRepository.findByDoctorIdAndAppointmentDateBetween(
                doctor.getUserId(), startDate, endDate);
        
        Map<String, Object> report = new HashMap<>();
        report.put("period", Map.of("startDate", startDate, "endDate", endDate));
        report.put("totalAppointments", appointments.size());
        report.put("completedAppointments", appointments.stream()
                .filter(a -> a.getStatus() == Appointment.AppointmentStatus.COMPLETED)
                .count());
        report.put("cancelledAppointments", appointments.stream()
                .filter(a -> a.getStatus() == Appointment.AppointmentStatus.CANCELLED)
                .count());
        
        return report;
    }

    /**
     * 批量操作预约
     */
    @Transactional
    public void batchOperateAppointments(Long userId, Map<String, Object> batchData) {
        Doctor doctor = validateDoctor(userId);

        @SuppressWarnings("unchecked")
        List<Long> appointmentIds = (List<Long>) batchData.get("appointmentIds");
        String operation = (String) batchData.get("operation");

        if (appointmentIds == null || appointmentIds.isEmpty()) {
            throw new RuntimeException("预约ID列表不能为空");
        }

        if (operation == null || operation.isEmpty()) {
            throw new RuntimeException("操作类型不能为空");
        }

        for (Long appointmentId : appointmentIds) {
            try {
                switch (operation.toLowerCase()) {
                    case "confirm":
                        confirmAppointment(userId, appointmentId);
                        break;
                    case "complete":
                        completeAppointment(userId, appointmentId, null);
                        break;
                    case "cancel":
                        cancelAppointment(userId, appointmentId, "医生批量取消");
                        break;
                    default:
                        throw new RuntimeException("不支持的操作类型: " + operation);
                }
            } catch (Exception e) {
                log.error("批量操作预约失败: appointmentId={}, operation={}, error={}",
                         appointmentId, operation, e.getMessage());
                // 继续处理其他预约，不中断整个批量操作
            }
        }
    }

    /**
     * 取消预约
     */
    @Transactional
    public void cancelAppointment(Long userId, Long appointmentId, String reason) {
        Doctor doctor = validateDoctor(userId);

        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));

        if (appointment.getSchedule() == null || !appointment.getSchedule().getDoctorId().equals(doctor.getUserId())) {
            throw new RuntimeException("无权限操作此预约");
        }

        appointment.setStatus(Appointment.AppointmentStatus.CANCELLED);
        if (reason != null && !reason.isEmpty()) {
            String currentNotes = appointment.getNotes() != null ? appointment.getNotes() : "";
            appointment.setNotes(currentNotes + "\n取消原因: " + reason);
        }
        appointmentRepository.save(appointment);

        // 释放排班名额
        DoctorSchedule schedule = appointment.getSchedule();
        if (schedule != null && schedule.getBookedSlots() > 0) {
            schedule.setBookedSlots(schedule.getBookedSlots() - 1);
            scheduleRepository.save(schedule);
        }
    }

    /**
     * 获取预约统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getAppointmentStats(Long userId, LocalDate startDate, LocalDate endDate) {
        Doctor doctor = validateDoctor(userId);

        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        List<Appointment> appointments = appointmentRepository.findByDoctorIdAndAppointmentDateBetween(
                doctor.getUserId(), startDate, endDate);

        Map<String, Object> stats = new HashMap<>();

        // 基础统计
        stats.put("totalAppointments", appointments.size());
        stats.put("bookedAppointments", appointments.stream()
                .filter(a -> a.getStatus() == Appointment.AppointmentStatus.BOOKED)
                .count());
        stats.put("completedAppointments", appointments.stream()
                .filter(a -> a.getStatus() == Appointment.AppointmentStatus.COMPLETED)
                .count());
        stats.put("cancelledAppointments", appointments.stream()
                .filter(a -> a.getStatus() == Appointment.AppointmentStatus.CANCELLED)
                .count());

        // 计算完成率
        long totalNonCancelled = appointments.stream()
                .filter(a -> a.getStatus() != Appointment.AppointmentStatus.CANCELLED)
                .count();
        double completionRate = totalNonCancelled > 0 ?
                (double) appointments.stream()
                        .filter(a -> a.getStatus() == Appointment.AppointmentStatus.COMPLETED)
                        .count() / totalNonCancelled * 100 : 0.0;
        stats.put("completionRate", completionRate);

        // 时间范围
        stats.put("period", Map.of("startDate", startDate, "endDate", endDate));

        return stats;
    }

    // 辅助方法
    private DoctorScheduleDTO convertToScheduleDTO(DoctorSchedule schedule) {
        DoctorScheduleDTO dto = new DoctorScheduleDTO();
        dto.setId(schedule.getId());
        dto.setDoctorId(schedule.getDoctorId());
        dto.setScheduleDate(schedule.getScheduleDate());
        dto.setStartTime(schedule.getStartTime());
        dto.setEndTime(schedule.getEndTime());
        dto.setTotalSlots(schedule.getTotalSlots());
        dto.setBookedSlots(schedule.getBookedSlots());
        dto.setAvailableSlots(schedule.getAvailableSlots());
        dto.setIsAvailable(schedule.hasAvailableSlots());
        return dto;
    }
    
    private AppointmentDTO convertToAppointmentDTO(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setUserId(appointment.getUserId());
        dto.setScheduleId(appointment.getScheduleId());
        dto.setProfileId(appointment.getProfileId());
        dto.setStatus(appointment.getStatus().name());
        dto.setNotes(appointment.getNotes());
        dto.setCreatedAt(appointment.getCreatedAt());
        return dto;
    }
    
    private Map<String, Object> convertToDoctorInfo(Doctor doctor) {
        Map<String, Object> info = new HashMap<>();
        info.put("id", doctor.getUserId());
        info.put("realName", doctor.getRealName());
        info.put("title", doctor.getTitle());
        info.put("specialty", doctor.getSpecialty());
        info.put("departmentId", doctor.getDepartmentId());
        if (doctor.getDepartment() != null) {
            info.put("departmentName", doctor.getDepartment().getName());
        }
        return info;
    }
}
