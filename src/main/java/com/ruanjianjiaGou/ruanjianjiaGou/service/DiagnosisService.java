package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.diagnosis.DiagnosisCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.diagnosis.DiagnosisVO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Appointment;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Diagnosis;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Doctor;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthProfile;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.AppointmentRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.DiagnosisRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.DoctorRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 诊断服务
 */
@Service
@RequiredArgsConstructor
public class DiagnosisService {
    
    private final DiagnosisRepository diagnosisRepository;
    private final AppointmentRepository appointmentRepository;
    private final DoctorRepository doctorRepository;
    
    /**
     * 创建诊断记录
     */
    @Transactional
    public DiagnosisVO createDiagnosis(Long doctorId, DiagnosisCreateDTO createDTO) {
        // 1. 验证预约是否存在
        Appointment appointment = appointmentRepository.findById(createDTO.getAppointmentId())
                .orElseThrow(() -> new RuntimeException("预约不存在"));
        
        // 2. 验证医生权限（可选，根据需求决定是否验证）
        // 这里按照您的要求不加任何限制，医生可以诊断任何预约
        
        // 3. 检查是否已有诊断记录
        Optional<Diagnosis> existingDiagnosis = diagnosisRepository.findByAppointmentId(createDTO.getAppointmentId());
        
        Diagnosis diagnosis;
        if (existingDiagnosis.isPresent()) {
            // 更新现有诊断记录
            diagnosis = existingDiagnosis.get();
        } else {
            // 创建新诊断记录
            diagnosis = new Diagnosis();
            diagnosis.setAppointmentId(createDTO.getAppointmentId());
            diagnosis.setDoctorId(doctorId);
        }
        
        // 4. 设置诊断信息
        diagnosis.setDiagnosis(createDTO.getDiagnosis());
        diagnosis.setTreatment(createDTO.getTreatment());
        diagnosis.setPrescription(createDTO.getPrescription());
        diagnosis.setNotes(createDTO.getNotes());
        diagnosis.setFollowUpAdvice(createDTO.getFollowUpAdvice());
        
        // 5. 保存诊断记录
        diagnosis = diagnosisRepository.save(diagnosis);
        
        // 6. 更新预约状态为已完成（如果需要）
        if (appointment.getStatus() != Appointment.AppointmentStatus.COMPLETED) {
            appointment.setStatus(Appointment.AppointmentStatus.COMPLETED);
            appointmentRepository.save(appointment);
        }
        
        // 7. 返回诊断信息
        return convertToDiagnosisVO(diagnosis);
    }
    
    /**
     * 获取预约的诊断记录
     */
    public DiagnosisVO getDiagnosisByAppointmentId(Long appointmentId) {
        Optional<Diagnosis> diagnosis = diagnosisRepository.findByAppointmentId(appointmentId);
        return diagnosis.map(this::convertToDiagnosisVO).orElse(null);
    }
    
    /**
     * 获取医生的诊断记录列表
     */
    public Page<DiagnosisVO> getDoctorDiagnoses(Long doctorId, Pageable pageable) {
        Page<Diagnosis> diagnoses = diagnosisRepository.findByDoctorId(doctorId, pageable);
        return diagnoses.map(this::convertToDiagnosisVO);
    }
    
    /**
     * 获取患者的诊断记录列表
     */
    public Page<DiagnosisVO> getPatientDiagnoses(Long userId, Pageable pageable) {
        Page<Diagnosis> diagnoses = diagnosisRepository.findByPatientId(userId, pageable);
        return diagnoses.map(this::convertToDiagnosisVO);
    }
    
    /**
     * 转换为DiagnosisVO
     */
    private DiagnosisVO convertToDiagnosisVO(Diagnosis diagnosis) {
        DiagnosisVO vo = new DiagnosisVO();
        vo.setId(diagnosis.getId());
        vo.setAppointmentId(diagnosis.getAppointmentId());
        vo.setDiagnosis(diagnosis.getDiagnosis());
        vo.setTreatment(diagnosis.getTreatment());
        vo.setPrescription(diagnosis.getPrescription());
        vo.setNotes(diagnosis.getNotes());
        vo.setFollowUpAdvice(diagnosis.getFollowUpAdvice());
        vo.setDiagnosisTime(diagnosis.getDiagnosisTime());
        
        // 设置医生信息
        vo.setDoctorId(diagnosis.getDoctorId());
        if (diagnosis.getDoctor() != null) {
            vo.setDoctorName(diagnosis.getDoctor().getRealName());
            vo.setDoctorTitle(diagnosis.getDoctor().getTitle());
        }
        
        // 设置患者信息
        if (diagnosis.getAppointment() != null) {
            Appointment appointment = diagnosis.getAppointment();
            if (appointment.getProfile() != null) {
                HealthProfile profile = appointment.getProfile();
                vo.setPatientName(profile.getProfileOwnerName());
                vo.setPatientGender(profile.getGender() != null ? profile.getGender().name() : null);
                vo.setPatientAge(profile.getAge());
            }
            if (appointment.getUser() != null) {
                vo.setPatientPhone(appointment.getUser().getPhoneNumber());
            }
        }
        
        return vo;
    }
}
