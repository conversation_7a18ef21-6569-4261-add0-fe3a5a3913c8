package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 电子处方服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PrescriptionService {
    
    private final EPrescriptionRepository prescriptionRepository;
    private final HealthProfileRepository healthProfileRepository;
    private final DoctorRepository doctorRepository;
    private final UserRepository userRepository;
    private final AppointmentRepository appointmentRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * 开具电子处方
     */
    public PrescriptionListDTO createPrescription(Long doctorId, PrescriptionCreateDTO createDTO) {
        log.info("医生 {} 为健康档案 {} 开具处方", doctorId, createDTO.getProfileId());
        
        // 验证健康档案是否存在
        HealthProfile healthProfile = healthProfileRepository.findById(createDTO.getProfileId())
                .orElseThrow(() -> new RuntimeException("健康档案不存在"));
        
        // 验证医生是否存在且已审核通过
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));
        
        if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
            throw new RuntimeException("医生尚未通过审核，无法开具处方");
        }
        
        // 创建处方
        EPrescription prescription = new EPrescription();
        prescription.setDoctorId(doctorId);
        prescription.setProfileId(createDTO.getProfileId());
        prescription.setConsultationId(createDTO.getConsultationId());
        prescription.setDiagnosis(createDTO.getDiagnosis());
        
        // 将药品列表转换为JSON字符串
        try {
            String medicationJson = objectMapper.writeValueAsString(createDTO.getMedications());
            prescription.setMedicationDetails(medicationJson);
        } catch (JsonProcessingException e) {
            log.error("药品信息JSON序列化失败", e);
            throw new RuntimeException("药品信息格式错误");
        }
        
        prescription = prescriptionRepository.save(prescription);
        log.info("处方开具成功，ID: {}", prescription.getId());
        
        return convertToPrescriptionListDTO(prescription);
    }
    
    /**
     * 获取患者的处方列表
     */
    @Transactional(readOnly = true)
    public Page<PrescriptionListDTO> getPatientPrescriptions(Long profileId, Long currentUserId, String userRole, int page, int size) {
        log.info("获取健康档案 {} 的处方列表，当前用户: {}, 角色: {}", profileId, currentUserId, userRole);
        
        // 验证健康档案是否存在
        HealthProfile healthProfile = healthProfileRepository.findById(profileId)
                .orElseThrow(() -> new RuntimeException("健康档案不存在"));
        
        // 权限验证
        if (!hasPermissionToViewPrescriptions(healthProfile, currentUserId, userRole)) {
            throw new RuntimeException("无权限查看该患者的处方记录");
        }
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<EPrescription> prescriptions = prescriptionRepository.findByProfileIdOrderByCreatedAtDesc(profileId, pageable);
        
        return prescriptions.map(this::convertToPrescriptionListDTO);
    }
    
    /**
     * 获取医生开具的处方列表
     */
    @Transactional(readOnly = true)
    public Page<PrescriptionListDTO> getDoctorPrescriptions(Long doctorId, int page, int size) {
        log.info("获取医生 {} 开具的处方列表", doctorId);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<EPrescription> prescriptions = prescriptionRepository.findByDoctorIdOrderByCreatedAtDesc(doctorId, pageable);
        
        return prescriptions.map(this::convertToPrescriptionListDTO);
    }
    
    /**
     * 检查用户是否有权限查看处方
     */
    private boolean hasPermissionToViewPrescriptions(HealthProfile healthProfile, Long currentUserId, String userRole) {
        // 健康档案的管理人可以查看
        if (healthProfile.getManagingUserId().equals(currentUserId)) {
            return true;
        }
        
        // 医生可以查看自己开具的处方
        if ("DOCTOR".equals(userRole)) {
            // 检查是否为该患者服务过的医生
            long prescriptionCount = prescriptionRepository.countByDoctorId(currentUserId);
            if (prescriptionCount > 0) {
                // 进一步检查是否为该特定患者开具过处方
                Pageable pageable = PageRequest.of(0, 1);
                Page<EPrescription> doctorPrescriptions = prescriptionRepository.findByDoctorIdAndProfileId(
                        currentUserId, healthProfile.getId(), pageable);
                return !doctorPrescriptions.isEmpty();
            }
        }
        
        return false;
    }
    
    /**
     * 转换为处方列表DTO
     */
    private PrescriptionListDTO convertToPrescriptionListDTO(EPrescription prescription) {
        PrescriptionListDTO dto = new PrescriptionListDTO();
        dto.setId(prescription.getId());
        dto.setDoctorId(prescription.getDoctorId());
        dto.setProfileId(prescription.getProfileId());
        dto.setConsultationId(prescription.getConsultationId());
        dto.setDiagnosis(prescription.getDiagnosis());
        dto.setCreatedAt(prescription.getCreatedAt());
        
        // 获取医生信息
        doctorRepository.findById(prescription.getDoctorId()).ifPresent(doctor -> {
            dto.setDoctorName(doctor.getRealName());
            if (doctor.getDepartment() != null) {
                dto.setDepartmentName(doctor.getDepartment().getName());
            }
        });
        
        // 获取患者信息
        healthProfileRepository.findById(prescription.getProfileId()).ifPresent(profile -> {
            dto.setPatientName(profile.getProfileOwnerName());
        });
        
        // 解析药品信息
        try {
            MedicationDTO[] medications = objectMapper.readValue(prescription.getMedicationDetails(), MedicationDTO[].class);
            dto.setMedications(Arrays.asList(medications));
        } catch (JsonProcessingException e) {
            log.error("药品信息JSON反序列化失败", e);
            dto.setMedications(List.of());
        }
        
        return dto;
    }

    // ==================== 预约相关的诊断处方方法 ====================

    /**
     * 为预约创建诊断处方
     * 医生可以对任何预约进行诊断，无时间限制
     */
    @Transactional
    public PrescriptionListDTO createPrescriptionForAppointment(Long doctorId, PrescriptionCreateDTO createDTO) {
        log.info("医生 {} 为预约 {} 开具诊断处方", doctorId, createDTO.getAppointmentId());

        // 验证预约是否存在
        if (createDTO.getAppointmentId() != null) {
            Appointment appointment = appointmentRepository.findById(createDTO.getAppointmentId())
                    .orElseThrow(() -> new RuntimeException("预约不存在"));

            // 自动设置健康档案ID
            if (createDTO.getProfileId() == null) {
                createDTO.setProfileId(appointment.getProfileId());
            }

            // 验证预约的健康档案ID与传入的是否一致
            if (!appointment.getProfileId().equals(createDTO.getProfileId())) {
                throw new RuntimeException("预约的健康档案ID与传入的不一致");
            }
        }

        // 验证健康档案是否存在
        HealthProfile healthProfile = healthProfileRepository.findById(createDTO.getProfileId())
                .orElseThrow(() -> new RuntimeException("健康档案不存在"));

        // 验证医生是否存在且已审核通过
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));

        if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
            throw new RuntimeException("医生尚未通过审核，无法开具处方");
        }

        // 创建处方
        EPrescription prescription = new EPrescription();
        prescription.setDoctorId(doctorId);
        prescription.setProfileId(createDTO.getProfileId());
        prescription.setConsultationId(createDTO.getConsultationId());
        prescription.setDiagnosis(createDTO.getDiagnosis());

        // 将药品列表转换为JSON字符串
        try {
            String medicationJson = objectMapper.writeValueAsString(createDTO.getMedications());
            prescription.setMedicationDetails(medicationJson);
        } catch (JsonProcessingException e) {
            log.error("药品信息JSON序列化失败", e);
            throw new RuntimeException("药品信息格式错误");
        }

        prescription = prescriptionRepository.save(prescription);
        log.info("诊断处方开具成功，ID: {}", prescription.getId());

        // 更新预约状态为已完成
        if (createDTO.getAppointmentId() != null) {
            Appointment appointment = appointmentRepository.findById(createDTO.getAppointmentId()).get();
            if (appointment.getStatus() != Appointment.AppointmentStatus.COMPLETED) {
                appointment.setStatus(Appointment.AppointmentStatus.COMPLETED);
                appointmentRepository.save(appointment);
                log.info("预约 {} 状态已更新为已完成", createDTO.getAppointmentId());
            }
        }

        return convertToPrescriptionListDTO(prescription);
    }

    /**
     * 根据预约ID获取诊断处方
     */
    @Transactional(readOnly = true)
    public PrescriptionListDTO getPrescriptionByAppointmentId(Long appointmentId) {
        log.info("获取预约 {} 的诊断处方", appointmentId);

        // 验证预约是否存在
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));

        // 查找该健康档案的处方记录，按时间倒序
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<EPrescription> prescriptions = prescriptionRepository.findByProfileIdOrderByCreatedAtDesc(
                appointment.getProfileId(), pageable);

        // 返回最新的处方记录（可以认为是该预约的诊断处方）
        if (!prescriptions.isEmpty()) {
            return convertToPrescriptionListDTO(prescriptions.getContent().get(0));
        }

        return null;
    }

    /**
     * 检查预约是否已有诊断处方
     */
    @Transactional(readOnly = true)
    public boolean hasAppointmentPrescription(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new RuntimeException("预约不存在"));

        // 检查该健康档案是否有处方记录
        long count = prescriptionRepository.countByProfileId(appointment.getProfileId());
        return count > 0;
    }
}
