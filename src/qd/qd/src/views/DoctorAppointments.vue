<template>
  <div class="doctor-appointments-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>👥 预约管理</h1>
          <p class="header-subtitle">管理患者预约和诊疗记录</p>
        </div>
        <div class="header-actions">
          <button @click="refreshData" class="refresh-btn" :disabled="loading">
            <span class="refresh-icon" :class="{ 'spinning': loading }">🔄</span>
            刷新
          </button>
          <button @click="exportData" class="export-btn">
            📊 导出数据
          </button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card urgent">
        <div class="stat-icon">🚨</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.urgent }}</div>
          <div class="stat-label">紧急预约</div>
        </div>
      </div>
      <div class="stat-card today">
        <div class="stat-icon">📅</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.today }}</div>
          <div class="stat-label">今日预约</div>
        </div>
      </div>
      <div class="stat-card booked">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.booked }}</div>
          <div class="stat-label">待确认</div>
        </div>
      </div>
      <div class="stat-card completed">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <AppointmentList
        :appointments="appointments"
        :loading="loading"
        :total-elements="totalElements"
        :total-pages="totalPages"
        :current-page="currentPage"
        :page-size="pageSize"
        @refresh="refreshData"
        @view="showAppointmentDetail"
        @confirm="confirmAppointment"
        @complete="completeAppointment"
        @addRecord="showMedicalRecordForm"
        @viewHistory="showPatientHistory"
        @cancel="cancelAppointment"
        @diagnose="showDiagnosisModal"
        @pageChange="handlePageChange"
        @filterChange="handleFilterChange"
      />
    </div>

    <!-- 成功消息提示 -->
    <div v-if="successMessage" class="success-message">
      <div class="message-content">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>

    <!-- 预约详情弹窗 -->
    <AppointmentDetail
      v-if="showDetailModal"
      :appointment-id="selectedAppointmentId"
      :appointment-data="selectedAppointment"
      @close="closeDetailModal"
      @success="handleDetailSuccess"
      @addRecord="showMedicalRecordForm"
      @viewHistory="showPatientHistory"
    />

    <!-- 诊疗记录表单弹窗 -->
    <MedicalRecordForm
      v-if="showRecordForm"
      :appointment="selectedAppointment"
      @close="closeRecordForm"
      @success="handleRecordSuccess"
    />

    <!-- 患者病历弹窗 -->
    <PatientHistory
      v-if="showHistoryModal"
      :patient="selectedPatient"
      @close="closeHistoryModal"
    />

    <!-- 诊断处方表单弹窗 -->
    <DiagnosisForm
      v-if="showDiagnosisForm"
      :appointment="selectedAppointment"
      @close="closeDiagnosisForm"
      @success="handleDiagnosisSuccess"
    />

    <!-- 确认对话框 -->
    <div v-if="showConfirmDialog" class="confirm-overlay">
      <div class="confirm-dialog">
        <div class="confirm-header">
          <h3>{{ confirmDialog.title }}</h3>
        </div>
        <div class="confirm-content">
          <p>{{ confirmDialog.message }}</p>
          <div v-if="confirmDialog.showInput" class="input-section">
            <label>{{ confirmDialog.inputLabel }}:</label>
            <textarea
              v-model="confirmDialog.inputValue"
              :placeholder="confirmDialog.inputPlaceholder"
              rows="3"
              class="confirm-input"
            ></textarea>
          </div>
        </div>
        <div class="confirm-actions">
          <button @click="closeConfirmDialog" class="cancel-btn">
            取消
          </button>
          <button 
            @click="executeConfirmAction" 
            :disabled="confirmLoading"
            class="confirm-btn"
          >
            <span v-if="confirmLoading">处理中...</span>
            <span v-else>{{ confirmDialog.confirmText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import AppointmentList from '@/components/AppointmentList.vue'
import AppointmentDetail from '@/components/AppointmentDetail.vue'
import MedicalRecordForm from '@/components/MedicalRecordForm.vue'
import PatientHistory from '@/components/PatientHistory.vue'
import DiagnosisForm from '@/components/DiagnosisForm.vue'
import { 
  getDoctorAppointments, 
  confirmAppointment as confirmAppointmentAPI,
  completeAppointment as completeAppointmentAPI,
  cancelAppointment as cancelAppointmentAPI
} from '@/api/appointments'
import { calculateAppointmentStats } from '@/utils/appointmentUtils'
import { formatDate } from '@/utils/dateUtils'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const confirmLoading = ref(false)
const appointments = ref([])
const successMessage = ref('')

// 分页数据
const totalElements = ref(0)
const totalPages = ref(0)
const currentPage = ref(1) // 改为从1开始，与API测试报告一致
const pageSize = ref(10)

// 筛选参数
const filters = ref({
  status: 'all',
  date: 'all',
  keyword: ''
})

// 弹窗状态
const showDetailModal = ref(false)
const showRecordForm = ref(false)
const showHistoryModal = ref(false)
const showConfirmDialog = ref(false)
const showDiagnosisForm = ref(false)

// 选中的数据
const selectedAppointmentId = ref(null)
const selectedAppointment = ref(null)
const selectedPatient = ref(null)

// 确认对话框数据
const confirmDialog = ref({
  title: '',
  message: '',
  confirmText: '确认',
  showInput: false,
  inputLabel: '',
  inputPlaceholder: '',
  inputValue: '',
  action: null,
  data: null
})

// 计算属性
const stats = computed(() => {
  return calculateAppointmentStats(appointments.value)
})

// 方法
const loadAppointments = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value, // 前端页码已经从1开始，直接使用
      size: pageSize.value
    }

    // 添加筛选参数
    if (filters.value.status !== 'all') {
      params.status = filters.value.status.toUpperCase()
    }
    if (filters.value.date !== 'all') {
      // 根据日期筛选添加相应参数
      const today = formatDate(new Date())
      switch (filters.value.date) {
        case 'today':
          params.date = today
          break
        case 'tomorrow':
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)
          params.date = formatDate(tomorrow)
          break
        // 可以添加更多日期筛选逻辑
      }
    }

    const response = await getDoctorAppointments(params)
    if (response.data.code === 200) {
      const data = response.data.data
      appointments.value = data.content || []
      totalElements.value = data.totalElements || 0
      totalPages.value = data.totalPages || 0
    } else {
      console.error('获取预约列表失败:', response.data.message)
      appointments.value = []
    }
  } catch (error) {
    console.error('获取预约列表失败:', error)
    appointments.value = []
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadAppointments()
}

const handlePageChange = (page) => {
  currentPage.value = page
  loadAppointments()
}

const handleFilterChange = (newFilters) => {
  filters.value = { ...filters.value, ...newFilters }
  currentPage.value = 1 // 重置到第一页
  loadAppointments()
}

const showAppointmentDetail = (appointment) => {
  selectedAppointmentId.value = appointment.id
  selectedAppointment.value = appointment
  showDetailModal.value = true
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedAppointmentId.value = null
  selectedAppointment.value = null
}

const handleDetailSuccess = (message) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
  loadAppointments() // 重新加载数据
}

const confirmAppointment = (appointment) => {
  confirmDialog.value = {
    title: '确认预约',
    message: `确定要确认患者 ${appointment.patient?.name || '未知'} 的预约吗？`,
    confirmText: '确认预约',
    showInput: false,
    action: 'confirm',
    data: appointment
  }
  showConfirmDialog.value = true
}

const completeAppointment = (appointment) => {
  confirmDialog.value = {
    title: '完成诊疗',
    message: `确定要标记患者 ${appointment.patient?.name || '未知'} 的诊疗为已完成吗？`,
    confirmText: '完成诊疗',
    showInput: true,
    inputLabel: '诊疗备注',
    inputPlaceholder: '请输入诊疗备注（可选）',
    inputValue: '',
    action: 'complete',
    data: appointment
  }
  showConfirmDialog.value = true
}

const cancelAppointment = (appointment) => {
  confirmDialog.value = {
    title: '取消预约',
    message: `确定要取消患者 ${appointment.patient?.name || '未知'} 的预约吗？`,
    confirmText: '取消预约',
    showInput: true,
    inputLabel: '取消原因',
    inputPlaceholder: '请输入取消原因',
    inputValue: '',
    action: 'cancel',
    data: appointment
  }
  showConfirmDialog.value = true
}

const showMedicalRecordForm = (appointment) => {
  selectedAppointment.value = appointment
  showRecordForm.value = true
}

const closeRecordForm = () => {
  showRecordForm.value = false
  selectedAppointment.value = null
}

const handleRecordSuccess = (message) => {
  closeRecordForm()
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
  loadAppointments() // 重新加载数据
}

const showPatientHistory = (patient) => {
  selectedPatient.value = patient
  showHistoryModal.value = true
}

const closeHistoryModal = () => {
  showHistoryModal.value = false
  selectedPatient.value = null
}

const showDiagnosisModal = (appointment) => {
  selectedAppointment.value = appointment
  showDiagnosisForm.value = true
}

const closeDiagnosisForm = () => {
  showDiagnosisForm.value = false
  selectedAppointment.value = null
}

const handleDiagnosisSuccess = (message) => {
  closeDiagnosisForm()
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
  loadAppointments() // 重新加载数据
}

const closeConfirmDialog = () => {
  showConfirmDialog.value = false
  confirmDialog.value = {
    title: '',
    message: '',
    confirmText: '确认',
    showInput: false,
    inputLabel: '',
    inputPlaceholder: '',
    inputValue: '',
    action: null,
    data: null
  }
}

const executeConfirmAction = async () => {
  const { action, data, inputValue } = confirmDialog.value

  if (!action || !data) return

  confirmLoading.value = true
  console.log('=== 执行预约操作（API测试报告格式）===')
  console.log('操作类型:', action)
  console.log('预约数据:', data)
  console.log('输入值:', inputValue)

  try {
    let response
    let successMsg = ''

    switch (action) {
      case 'confirm':
        console.log('=== 确认预约 ===')
        response = await confirmAppointmentAPI(data.id)
        successMsg = '预约确认成功！'
        break
      case 'complete':
        console.log('=== 完成诊疗（API测试报告格式）===')
        console.log('预约ID:', data.id)

        // 根据API测试报告，确保请求数据格式正确
        const completeData = {
          notes: inputValue?.trim() || '诊疗完成，患者恢复良好'
        }
        console.log('完成诊疗请求数据:', completeData)

        response = await completeAppointmentAPI(data.id, completeData)
        console.log('=== 完成诊疗API响应 ===')
        console.log('响应状态:', response.status)
        console.log('响应数据:', response.data)

        successMsg = '诊疗完成！'
        break
      case 'cancel':
        if (!inputValue.trim()) {
          alert('请输入取消原因')
          return
        }
        console.log('=== 取消预约 ===')
        response = await cancelAppointmentAPI(data.id, { reason: inputValue })
        successMsg = '预约已取消'
        break
      default:
        return
    }

    if (response.data.code === 200) {
      console.log('✅ 操作成功:', successMsg)
      closeConfirmDialog()
      successMessage.value = successMsg
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
      loadAppointments() // 重新加载数据
    } else {
      console.error('❌ 操作失败:', response.data)
      alert('操作失败：' + response.data.message)
    }
  } catch (error) {
    console.error('❌ 操作失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    console.error('错误状态码:', error.response?.status)

    if (error.response?.status === 401) {
      alert('登录已过期，请重新登录')
      userStore.logout()
      router.push('/login')
    } else if (error.response?.status === 403) {
      alert('没有权限执行此操作')
    } else if (error.response?.data?.message) {
      alert('操作失败：' + error.response.data.message)
    } else {
      alert('操作失败，请重试')
    }
  } finally {
    confirmLoading.value = false
  }
}

const exportData = () => {
  // 实现数据导出功能
  alert('数据导出功能开发中...')
}

// 生命周期
onMounted(() => {
  loadAppointments()
})
</script>

<style scoped>
.doctor-appointments-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #1e40af;
  font-size: 28px;
  font-weight: 700;
}

.header-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn,
.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.refresh-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.export-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-card.urgent .stat-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.stat-card.today .stat-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
}

.stat-card.booked .stat-icon {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.stat-card.completed .stat-icon {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.main-content {
  margin-bottom: 24px;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #dcfce7;
  color: #166534;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 确认对话框样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
}

.confirm-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.confirm-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.confirm-content {
  padding: 24px;
}

.confirm-content p {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

.input-section {
  margin-top: 16px;
}

.input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.confirm-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.confirm-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .doctor-appointments-page {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .confirm-dialog {
    width: 95%;
    margin: 20px;
  }
  
  .confirm-content {
    padding: 16px;
  }
  
  .confirm-actions {
    flex-direction: column;
  }
}
</style>
