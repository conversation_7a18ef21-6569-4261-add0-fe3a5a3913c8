# Debug Doctor Login and Permissions

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Debug Doctor Login and Permissions ===" -ForegroundColor Green

# Test different doctor phone numbers from database
$doctorPhones = @(
    "18610001001",  # <PERSON>
    "18610001002",  # <PERSON>
    "18610001003",  # Zhang <PERSON>
    "18610001004",  # <PERSON>
    "18610001005",  # <PERSON>
    "13900000002"   # Test Doctor
)

foreach ($phone in $doctorPhones) {
    Write-Host "`nTesting Doctor Phone: $phone" -ForegroundColor Yellow
    
    $loginData = @{
        phoneNumber = $phone
        password = "123456"
    } | ConvertTo-J<PERSON>

    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
        
        Write-Host "  ✅ Login Success" -ForegroundColor Green
        Write-Host "  User ID: $($loginResponse.data.user.id)" -ForegroundColor Cyan
        Write-Host "  Real Name: $($loginResponse.data.user.realName)" -ForegroundColor Cyan
        Write-Host "  Role: $($loginResponse.data.user.role)" -ForegroundColor Cyan
        Write-Host "  Phone: $($loginResponse.data.user.phoneNumber)" -ForegroundColor Cyan
        
        # Check if this is a doctor role
        if ($loginResponse.data.user.role -eq "DOCTOR") {
            Write-Host "  ✅ Confirmed DOCTOR role" -ForegroundColor Green
            
            # Test doctor appointments API with this account
            $token = $loginResponse.data.token
            $authHeaders = $headers.Clone()
            $authHeaders["Authorization"] = "Bearer $token"
            
            try {
                $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=5"
                $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
                
                Write-Host "  ✅ Doctor Appointments API Success" -ForegroundColor Green
                Write-Host "  Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
                
                if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
                    $appointment = $appointmentsResponse.data.content[0]
                    Write-Host "  Sample Appointment:" -ForegroundColor White
                    Write-Host "    ID: $($appointment.id)" -ForegroundColor Gray
                    Write-Host "    Patient: $($appointment.profileOwnerName)" -ForegroundColor Gray
                    Write-Host "    Status: $($appointment.status)" -ForegroundColor Gray
                    Write-Host "    Date: $($appointment.appointmentDate)" -ForegroundColor Gray
                    Write-Host "    Time: $($appointment.appointmentTime)" -ForegroundColor Gray
                    
                    # Check data completeness
                    if ($appointment.profileOwnerName -and $appointment.profileOwnerName -ne "null") {
                        Write-Host "    ✅ Patient name: $($appointment.profileOwnerName)" -ForegroundColor Green
                    } else {
                        Write-Host "    ❌ Patient name missing" -ForegroundColor Red
                    }
                    
                    if ($appointment.appointmentDate) {
                        Write-Host "    ✅ Appointment date: $($appointment.appointmentDate)" -ForegroundColor Green
                    } else {
                        Write-Host "    ❌ Appointment date missing" -ForegroundColor Red
                    }
                    
                    # This doctor has appointments - use for testing
                    Write-Host "  🎯 This doctor has appointments - good for testing!" -ForegroundColor Magenta
                    
                    # Test appointment detail
                    try {
                        $detailUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)"
                        $detailResponse = Invoke-RestMethod -Uri $detailUrl -Method GET -Headers $authHeaders
                        Write-Host "  ✅ Appointment Detail API Success" -ForegroundColor Green
                        
                        $detail = $detailResponse.data
                        Write-Host "    Detail Patient: $($detail.profileOwnerName)" -ForegroundColor Gray
                        Write-Host "    Detail Gender: $($detail.profileGender)" -ForegroundColor Gray
                        Write-Host "    Detail Age: $($detail.profileAge)" -ForegroundColor Gray
                        Write-Host "    Detail Phone: $($detail.profilePhoneNumber)" -ForegroundColor Gray
                        
                    } catch {
                        Write-Host "  ❌ Appointment Detail Failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
                
            } catch {
                Write-Host "  ❌ Doctor Appointments API Failed: $($_.Exception.Message)" -ForegroundColor Red
                if ($_.ErrorDetails.Message) {
                    Write-Host "  Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
                }
            }
            
        } else {
            Write-Host "  ⚠️ Not a doctor role: $($loginResponse.data.user.role)" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test with a known working patient account to create appointments
Write-Host "`nTesting Patient Account for Appointment Creation..." -ForegroundColor Yellow

$patientLoginData = @{
    phoneNumber = "***********"
    password = "123456"
} | ConvertTo-Json

try {
    $patientLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $patientLoginData -Headers $headers
    Write-Host "Patient Login Success: $($patientLoginResponse.data.user.realName)" -ForegroundColor Green
    
    $patientToken = $patientLoginResponse.data.token
    $patientHeaders = $headers.Clone()
    $patientHeaders["Authorization"] = "Bearer $patientToken"
    
    # Check patient appointments
    $patientAppointmentsUrl = "$baseUrl/api/appointments/my?page=1&size=10"
    $patientAppointmentsResponse = Invoke-RestMethod -Uri $patientAppointmentsUrl -Method GET -Headers $patientHeaders
    Write-Host "Patient has $($patientAppointmentsResponse.data.totalElements) appointments" -ForegroundColor Green
    
    if ($patientAppointmentsResponse.data.content -and $patientAppointmentsResponse.data.content.Count -gt 0) {
        $patientAppointment = $patientAppointmentsResponse.data.content[0]
        Write-Host "Patient appointment with doctor: $($patientAppointment.doctorName)" -ForegroundColor Cyan
        Write-Host "Doctor ID: $($patientAppointment.doctorId)" -ForegroundColor Cyan
        Write-Host "Status: $($patientAppointment.status)" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "Patient test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Green
