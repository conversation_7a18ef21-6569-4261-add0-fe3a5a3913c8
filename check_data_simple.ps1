# Check Doctor and Department Data

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Check Doctor and Department Data ===" -ForegroundColor Green

# 1. User Login
Write-Host "`n1. User Login" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login Success" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Get Departments
Write-Host "`n2. Get Departments" -ForegroundColor Yellow
try {
    $departmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments" -Method GET -Headers $headers
    Write-Host "Get Departments Success" -ForegroundColor Green
    Write-Host "Department Count: $($departmentsResponse.data.Count)" -ForegroundColor Cyan
    
    $departments = $departmentsResponse.data
    
    if ($departments.Count -gt 0) {
        Write-Host "`nDepartment List:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $departments.Count; $i++) {
            $dept = $departments[$i]
            Write-Host "[$($i+1)] ID: $($dept.id) | Name: $($dept.name)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Get Departments Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Check Doctors in Each Department
Write-Host "`n3. Check Doctors in Each Department" -ForegroundColor Yellow
foreach ($dept in $departments) {
    Write-Host "`nDepartment: $($dept.name) (ID: $($dept.id))" -ForegroundColor Cyan
    
    try {
        $doctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/departments/$($dept.id)/doctors?page=1&size=20" -Method GET -Headers $headers
        $doctorCount = $doctorsResponse.data.totalElements
        $doctors = $doctorsResponse.data.content
        
        Write-Host "Doctor Count: $doctorCount" -ForegroundColor Green
        
        if ($doctors.Count -gt 0) {
            Write-Host "Doctors:" -ForegroundColor White
            for ($j = 0; $j -lt [Math]::Min($doctors.Count, 3); $j++) {
                $doctor = $doctors[$j]
                Write-Host "  - $($doctor.realName) | Title: $($doctor.title) | Status: $($doctor.status)" -ForegroundColor Gray
            }
            if ($doctors.Count -gt 3) {
                Write-Host "  ... and $($doctors.Count - 3) more doctors" -ForegroundColor Gray
            }
        } else {
            Write-Host "No doctors in this department" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Get Department Doctors Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. Search All Doctors
Write-Host "`n4. Search All Doctors" -ForegroundColor Yellow
try {
    $allDoctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/appointments/doctors?page=1&size=50" -Method GET -Headers $headers
    Write-Host "Get All Doctors Success" -ForegroundColor Green
    Write-Host "Total Doctors: $($allDoctorsResponse.data.totalElements)" -ForegroundColor Cyan
    
    $allDoctors = $allDoctorsResponse.data.content
    
    if ($allDoctors.Count -gt 0) {
        Write-Host "`nAll Doctors List:" -ForegroundColor Cyan
        for ($i = 0; $i -lt [Math]::Min($allDoctors.Count, 10); $i++) {
            $doctor = $allDoctors[$i]
            Write-Host "[$($i+1)] $($doctor.realName) | Dept ID: $($doctor.departmentId) | Title: $($doctor.title) | Status: $($doctor.status)" -ForegroundColor White
        }
        if ($allDoctors.Count -gt 10) {
            Write-Host "... and $($allDoctors.Count - 10) more doctors" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "Get All Doctors Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Data Check Complete ===" -ForegroundColor Green
