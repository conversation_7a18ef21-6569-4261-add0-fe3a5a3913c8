# Simple Doctor Appointments Test

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Simple Doctor Appointments Test ===" -ForegroundColor Green

# 1. Doctor Login
Write-Host "`n1. Doctor Login" -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    Write-Host "Doctor ID: $($loginResponse.data.user.id)" -ForegroundColor Cyan
    Write-Host "Doctor Role: $($loginResponse.data.user.role)" -ForegroundColor Cyan
    Write-Host "Doctor Name: $($loginResponse.data.user.realName)" -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
    $headers["Authorization"] = "Bearer $token"
} catch {
    Write-Host "Doctor Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test Doctor Appointments API
Write-Host "`n2. Test Doctor Appointments API" -ForegroundColor Yellow

try {
    $url = "$baseUrl/api/doctor/appointments/my"
    $params = "?page=1&size=10"
    $fullUrl = $url + $params
    
    Write-Host "Request URL: $fullUrl" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $headers
    Write-Host "API Response Success" -ForegroundColor Green
    Write-Host "Response Code: $($response.code)" -ForegroundColor Cyan
    Write-Host "Total Appointments: $($response.data.totalElements)" -ForegroundColor Cyan
    
    if ($response.data.content -and $response.data.content.Count -gt 0) {
        Write-Host "`nFirst Appointment Details:" -ForegroundColor White
        $appointment = $response.data.content[0]
        
        Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
        Write-Host "  Created: $($appointment.createdAt)" -ForegroundColor Gray
        
        # Patient Information
        Write-Host "  Patient Name: $($appointment.profileOwnerName)" -ForegroundColor Yellow
        Write-Host "  Patient Gender: $($appointment.profileGender)" -ForegroundColor Yellow
        Write-Host "  Patient Age: $($appointment.profileAge)" -ForegroundColor Yellow
        Write-Host "  Patient Phone: $($appointment.profilePhoneNumber)" -ForegroundColor Yellow
        Write-Host "  Patient Birth Date: $($appointment.profileBirthDate)" -ForegroundColor Yellow
        
        # Doctor Information
        Write-Host "  Doctor Name: $($appointment.doctorName)" -ForegroundColor Cyan
        Write-Host "  Doctor Title: $($appointment.doctorTitle)" -ForegroundColor Cyan
        Write-Host "  Department: $($appointment.departmentName)" -ForegroundColor Cyan
        
        # Schedule Information
        Write-Host "  Appointment Date: $($appointment.appointmentDate)" -ForegroundColor Green
        Write-Host "  Appointment Time: $($appointment.appointmentTime)" -ForegroundColor Green
        Write-Host "  End Time: $($appointment.endTime)" -ForegroundColor Green
        
        # Check completeness
        Write-Host "`nData Completeness Check:" -ForegroundColor White
        if ($appointment.profileOwnerName) {
            Write-Host "  ✅ Patient name: OK" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Patient name: Missing" -ForegroundColor Red
        }
        
        if ($appointment.appointmentDate) {
            Write-Host "  ✅ Appointment date: OK" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Appointment date: Missing" -ForegroundColor Red
        }
        
        if ($appointment.doctorName) {
            Write-Host "  ✅ Doctor name: OK" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Doctor name: Missing" -ForegroundColor Red
        }
        
    } else {
        Write-Host "No appointments found" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "API Call Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 3. Test Patient Login and Appointments
Write-Host "`n3. Test Patient Appointments for Comparison" -ForegroundColor Yellow

$patientLoginData = @{
    phoneNumber = "13800000011"
    password = "123456"
} | ConvertTo-Json

try {
    $patientLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $patientLoginData -Headers @{"Content-Type" = "application/json"}
    Write-Host "Patient Login Success: $($patientLoginResponse.data.user.realName)" -ForegroundColor Green
    
    $patientHeaders = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $($patientLoginResponse.data.token)"
    }
    
    $patientUrl = "$baseUrl/api/appointments/my"
    $patientParams = "?page=1&size=10"
    $patientFullUrl = $patientUrl + $patientParams
    
    $patientResponse = Invoke-RestMethod -Uri $patientFullUrl -Method GET -Headers $patientHeaders
    Write-Host "Patient has $($patientResponse.data.totalElements) appointments" -ForegroundColor Green
    
    if ($patientResponse.data.content -and $patientResponse.data.content.Count -gt 0) {
        $patientAppointment = $patientResponse.data.content[0]
        Write-Host "Patient's appointment with doctor: $($patientAppointment.doctorName)" -ForegroundColor Cyan
        Write-Host "Appointment date: $($patientAppointment.appointmentDate)" -ForegroundColor Cyan
        Write-Host "Appointment status: $($patientAppointment.status)" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "Patient test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
