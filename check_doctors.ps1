# Check Doctor Status

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Check Doctor Status ===" -ForegroundColor Green

# Admin login to check doctor status
Write-Host "`n1. Admin Login" -ForegroundColor Yellow

$adminLoginData = @{
    phoneNumber = "19999999999"
    password = "123456"
} | ConvertTo-Json

try {
    $adminLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $adminLoginData -Headers $headers
    Write-Host "Admin Login Success" -ForegroundColor Green
    
    $adminToken = $adminLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $adminToken"
    
    # Get all doctors
    Write-Host "`n2. Get All Doctors" -ForegroundColor Yellow
    
    try {
        $doctorsResponse = Invoke-RestMethod -Uri "$baseUrl/api/admin/doctors" -Method GET -Headers $authHeaders
        
        Write-Host "Doctors Found: $($doctorsResponse.data.Count)" -ForegroundColor Cyan
        
        foreach ($doctor in $doctorsResponse.data) {
            Write-Host "`nDoctor Info:" -ForegroundColor White
            Write-Host "  ID: $($doctor.id)" -ForegroundColor Gray
            Write-Host "  Name: $($doctor.name)" -ForegroundColor Yellow
            Write-Host "  Phone: $($doctor.phoneNumber)" -ForegroundColor Yellow
            Write-Host "  Status: $($doctor.status)" -ForegroundColor Magenta
            Write-Host "  Department: $($doctor.departmentName)" -ForegroundColor Cyan
            
            if ($doctor.status -eq "APPROVED") {
                Write-Host "  ✅ This doctor can login and use diagnosis features" -ForegroundColor Green
            } else {
                Write-Host "  ❌ This doctor cannot login (status: $($doctor.status))" -ForegroundColor Red
            }
        }
        
        # Try to approve a doctor if needed
        $pendingDoctors = $doctorsResponse.data | Where-Object { $_.status -eq "PENDING" }
        if ($pendingDoctors.Count -gt 0) {
            Write-Host "`n3. Approve Pending Doctors" -ForegroundColor Yellow
            
            foreach ($pendingDoctor in $pendingDoctors) {
                Write-Host "Approving doctor: $($pendingDoctor.name) ($($pendingDoctor.phoneNumber))" -ForegroundColor Cyan
                
                try {
                    $approveResponse = Invoke-RestMethod -Uri "$baseUrl/api/admin/doctors/$($pendingDoctor.id)/approve" -Method POST -Headers $authHeaders
                    Write-Host "  ✅ Doctor approved successfully" -ForegroundColor Green
                } catch {
                    Write-Host "  ❌ Failed to approve doctor: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
    } catch {
        Write-Host "Failed to get doctors: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Admin login failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Check Complete ===" -ForegroundColor Green

Write-Host "`nApproved doctors can:" -ForegroundColor Yellow
Write-Host "1. Login to the system" -ForegroundColor White
Write-Host "2. Access doctor appointment management" -ForegroundColor White
Write-Host "3. Use diagnosis and prescription features" -ForegroundColor White
