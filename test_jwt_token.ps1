# Test JWT Token and User Data

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Test JWT Token and User Data ===" -ForegroundColor Green

# 1. Test Login and Token Generation
Write-Host "`n1. Test Login and Token Generation" -ForegroundColor Yellow

$loginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginData -Headers $headers
    Write-Host "Login API Success" -ForegroundColor Green
    Write-Host "Response Code: $($loginResponse.code)" -ForegroundColor Cyan
    Write-Host "Message: $($loginResponse.message)" -ForegroundColor Cyan
    
    # Check token
    if ($loginResponse.data.token) {
        Write-Host "Token Generated: YES (Length: $($loginResponse.data.token.Length))" -ForegroundColor Green
        $token = $loginResponse.data.token
        Write-Host "Token Preview: $($token.Substring(0, [Math]::Min(50, $token.Length)))..." -ForegroundColor Gray
    } else {
        Write-Host "Token Generated: NO" -ForegroundColor Red
        exit 1
    }
    
    # Check user data in response
    Write-Host "`nUser Data in Login Response:" -ForegroundColor White
    Write-Host "  ID: '$($loginResponse.data.user.id)'" -ForegroundColor Gray
    Write-Host "  Real Name: '$($loginResponse.data.user.realName)'" -ForegroundColor Gray
    Write-Host "  Role: '$($loginResponse.data.user.role)'" -ForegroundColor Gray
    Write-Host "  Phone: '$($loginResponse.data.user.phoneNumber)'" -ForegroundColor Gray
    
    # Check if user data is empty
    if (-not $loginResponse.data.user.id -or $loginResponse.data.user.id -eq "") {
        Write-Host "❌ User ID is empty in login response!" -ForegroundColor Red
    }
    
    if (-not $loginResponse.data.user.role -or $loginResponse.data.user.role -eq "") {
        Write-Host "❌ User role is empty in login response!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
    exit 1
}

# 2. Test User Profile API
Write-Host "`n2. Test User Profile API" -ForegroundColor Yellow

$authHeaders = $headers.Clone()
$authHeaders["Authorization"] = "Bearer $token"

try {
    $profileResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/profile" -Method GET -Headers $authHeaders
    Write-Host "Profile API Success" -ForegroundColor Green
    Write-Host "Response Code: $($profileResponse.code)" -ForegroundColor Cyan
    
    Write-Host "`nProfile Data:" -ForegroundColor White
    Write-Host "  ID: '$($profileResponse.data.id)'" -ForegroundColor Gray
    Write-Host "  Real Name: '$($profileResponse.data.realName)'" -ForegroundColor Gray
    Write-Host "  Role: '$($profileResponse.data.role)'" -ForegroundColor Gray
    Write-Host "  Phone: '$($profileResponse.data.phoneNumber)'" -ForegroundColor Gray
    
} catch {
    Write-Host "Profile API Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 3. Test Doctor Appointments API with detailed error info
Write-Host "`n3. Test Doctor Appointments API" -ForegroundColor Yellow

try {
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=5"
    Write-Host "Request URL: $appointmentsUrl" -ForegroundColor Gray
    Write-Host "Authorization Header: Bearer $($token.Substring(0, 20))..." -ForegroundColor Gray
    
    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
    Write-Host "Doctor Appointments API Success" -ForegroundColor Green
    Write-Host "Response Code: $($appointmentsResponse.code)" -ForegroundColor Cyan
    Write-Host "Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
} catch {
    Write-Host "Doctor Appointments API Failed" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Error Message: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
    
    # Try to get more details from the response
    try {
        $errorResponse = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "Parsed Error Response:" -ForegroundColor Red
        Write-Host "  Code: $($errorResponse.code)" -ForegroundColor Red
        Write-Host "  Message: $($errorResponse.message)" -ForegroundColor Red
    } catch {
        Write-Host "Could not parse error response" -ForegroundColor Red
    }
}

# 4. Test with a different approach - check if user exists in database
Write-Host "`n4. Test Database User Existence" -ForegroundColor Yellow

# Try to login with different known accounts
$testAccounts = @(
    @{ phone = "***********"; role = "RESIDENT" },
    @{ phone = "***********"; role = "ADMIN" }
)

foreach ($account in $testAccounts) {
    Write-Host "`nTesting $($account.role) account: $($account.phone)" -ForegroundColor Cyan
    
    $testLoginData = @{
        phoneNumber = $account.phone
        password = "123456"
    } | ConvertTo-Json
    
    try {
        $testLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $testLoginData -Headers $headers
        Write-Host "  ✅ Login Success" -ForegroundColor Green
        Write-Host "  User ID: '$($testLoginResponse.data.user.id)'" -ForegroundColor Gray
        Write-Host "  Role: '$($testLoginResponse.data.user.role)'" -ForegroundColor Gray
        Write-Host "  Name: '$($testLoginResponse.data.user.realName)'" -ForegroundColor Gray
        
        if ($testLoginResponse.data.user.id -and $testLoginResponse.data.user.role) {
            Write-Host "  ✅ User data is complete" -ForegroundColor Green
        } else {
            Write-Host "  ❌ User data is incomplete" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== JWT Token Test Complete ===" -ForegroundColor Green

# Summary
Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "1. Check if JWT token is being generated correctly" -ForegroundColor White
Write-Host "2. Check if user data is being returned in login response" -ForegroundColor White
Write-Host "3. Check if SecurityUtils can parse the token correctly" -ForegroundColor White
Write-Host "4. Check if the doctor account has the correct role in database" -ForegroundColor White
