# Simple Diagnosis Test

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Diagnosis Function Test ===" -ForegroundColor Green

# Doctor login
Write-Host "`n1. Doctor Login" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "***********"
    password = "123456"
} | ConvertTo-<PERSON>son

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $doctorToken"
    
    # Get appointments
    Write-Host "`n2. Get Appointments" -ForegroundColor Yellow
    
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
    
    Write-Host "Appointments Retrieved: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        $appointment = $appointmentsResponse.data.content[0]
        
        Write-Host "`nAppointment Details:" -ForegroundColor White
        Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  Patient: $($appointment.profileOwnerName)" -ForegroundColor Yellow
        Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
        Write-Host "  Has Diagnosis: $($appointment.hasDiagnosis)" -ForegroundColor Magenta
        
        # Create diagnosis prescription
        Write-Host "`n3. Create Diagnosis Prescription" -ForegroundColor Yellow
        
        $prescriptionData = @{
            profileId = $appointment.profileId
            appointmentId = $appointment.id
            diagnosis = "Common cold with mild symptoms"
            medications = @(
                @{
                    name = "Paracetamol"
                    specification = "500mg"
                    quantity = 10
                    frequency = "3 times daily"
                    dosage = "1 tablet after meals"
                    notes = "Take with water"
                }
            )
        } | ConvertTo-Json -Depth 3
        
        try {
            $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescribe"
            $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $authHeaders
            
            Write-Host "Prescription Created Successfully!" -ForegroundColor Green
            Write-Host "Prescription ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
            
            # Verify appointment status update
            Write-Host "`n4. Verify Status Update" -ForegroundColor Yellow
            
            $updatedResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
            $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $appointment.id }
            
            if ($updatedAppointment) {
                Write-Host "Updated Status: $($updatedAppointment.status)" -ForegroundColor Green
                Write-Host "Has Diagnosis: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                
                if ($updatedAppointment.hasDiagnosis -eq $true) {
                    Write-Host "`nSUCCESS: Diagnosis function is working!" -ForegroundColor Green
                } else {
                    Write-Host "`nISSUE: Diagnosis status not updated" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "Prescription Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "No appointments found" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Test Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green

Write-Host "`nFrontend URLs:" -ForegroundColor Cyan
Write-Host "- Frontend: http://localhost:5174" -ForegroundColor White
Write-Host "- Doctor Appointments: http://localhost:5174/doctor/appointments" -ForegroundColor White
Write-Host "- Test Account: *********** / 123456" -ForegroundColor White

Write-Host "`nDiagnosis Features:" -ForegroundColor Yellow
Write-Host "1. Click diagnosis button in appointment list" -ForegroundColor White
Write-Host "2. Fill diagnosis and prescription details" -ForegroundColor White
Write-Host "3. Submit to create prescription" -ForegroundColor White
Write-Host "4. Appointment status updates automatically" -ForegroundColor White
