# Simple Prescription Test

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== Simple Prescription Test ===" -ForegroundColor Green

# Test doctor login and appointments
Write-Host "`n1. Doctor Login Test" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "18610001001"
    password = "123456"
} | ConvertTo-Json

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "Doctor Login Success" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $doctorToken"
    
    # Test appointments API
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my"
    $params = "?page=1&size=10"
    $fullUrl = $appointmentsUrl + $params
    
    $appointmentsResponse = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $authHeaders
    Write-Host "Doctor Appointments API Success" -ForegroundColor Green
    Write-Host "Total Appointments: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        Write-Host "`nFirst Appointment Details:" -ForegroundColor White
        $appointment = $appointmentsResponse.data.content[0]
        
        Write-Host "  ID: $($appointment.id)" -ForegroundColor Gray
        Write-Host "  Status: $($appointment.status)" -ForegroundColor Gray
        Write-Host "  Patient Name: '$($appointment.profileOwnerName)'" -ForegroundColor Yellow
        Write-Host "  Patient Gender: '$($appointment.profileGender)'" -ForegroundColor Yellow
        Write-Host "  Patient Age: '$($appointment.profileAge)'" -ForegroundColor Yellow
        Write-Host "  Patient Phone: '$($appointment.profilePhoneNumber)'" -ForegroundColor Yellow
        Write-Host "  Diagnosis Status: '$($appointment.diagnosisStatus)'" -ForegroundColor Magenta
        Write-Host "  Profile ID: $($appointment.profileId)" -ForegroundColor Gray
        
        # Test prescription creation
        Write-Host "`n2. Create Prescription with Diagnosis" -ForegroundColor Yellow
        
        $prescriptionData = @{
            profileId = $appointment.profileId
            appointmentId = $appointment.id
            diagnosis = "Test diagnosis for headache"
            medications = @(
                @{
                    name = "Ibuprofen"
                    specification = "200mg"
                    quantity = 10
                    frequency = "Twice daily"
                    dosage = "1 tablet after meals"
                    notes = "Take with food"
                }
            )
        } | ConvertTo-Json -Depth 3
        
        try {
            $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($appointment.id)/prescribe"
            $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $authHeaders
            
            Write-Host "Prescription Created Successfully" -ForegroundColor Green
            Write-Host "Prescription ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
            Write-Host "Diagnosis: $($prescriptionResponse.data.prescription.diagnosis)" -ForegroundColor Yellow
            
            # Check updated appointment status
            Write-Host "`n3. Check Updated Appointment Status" -ForegroundColor Yellow
            
            $updatedResponse = Invoke-RestMethod -Uri $fullUrl -Method GET -Headers $authHeaders
            $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $appointment.id }
            
            if ($updatedAppointment) {
                Write-Host "Updated Status: $($updatedAppointment.status)" -ForegroundColor Green
                Write-Host "Updated Diagnosis Status: $($updatedAppointment.diagnosisStatus)" -ForegroundColor Magenta
                Write-Host "Has Diagnosis: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                
                if ($updatedAppointment.hasDiagnosis -eq $true) {
                    Write-Host "`nSUCCESS: Prescription-based diagnosis is working!" -ForegroundColor Green
                } else {
                    Write-Host "`nISSUE: Diagnosis status not updated" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "Prescription Creation Failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "No appointments found for this doctor" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green

Write-Host "`nSummary of Prescription-Based Diagnosis:" -ForegroundColor Cyan
Write-Host "1. Uses existing e_prescriptions table" -ForegroundColor White
Write-Host "2. Doctors can prescribe with diagnosis for any appointment" -ForegroundColor White
Write-Host "3. No time restrictions on diagnosis" -ForegroundColor White
Write-Host "4. Appointment status updated to COMPLETED after prescription" -ForegroundColor White
Write-Host "5. Diagnosis status shown in appointment list" -ForegroundColor White

Write-Host "`nAPI Endpoints:" -ForegroundColor Yellow
Write-Host "- POST /api/doctor/appointments/{id}/prescribe" -ForegroundColor White
Write-Host "- GET /api/doctor/appointments/{id}/prescription" -ForegroundColor White
Write-Host "- GET /api/doctor/prescriptions/my" -ForegroundColor White
